# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):$(prefix)$(attr)$(sensinst)") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B$(fruid) C$(type) N$(sensinst) @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):$(prefix)$(attr)$(sensinst)P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
$(noalias="")  alias("$(dev):$(aliasprefix)$(attr)$(sensinst)")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):$(prefix)$(attr)$(sensinst)P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B$(fruid) C$(type) N$(sensinst) @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
$(noalias="")  alias("$(dev):$(aliasprefix)$(attr)$(sensinst)P")
}
