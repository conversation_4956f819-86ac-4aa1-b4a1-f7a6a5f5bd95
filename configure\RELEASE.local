# RELEASE.local - 本地配置文件
# 根据您的实际环境修改以下路径

# ==========================================================
# 如果您有 RELEASE_SITE 文件，可以取消注释以下行
# ==========================================================
# -include $(TOP)/../../RELEASE_SITE
# -include $(TOP)/RELEASE_SITE
# -include $(TOP)/../../RELEASE_SITE.check

# ==========================================================
# 定义模块版本
# ==========================================================
ASYN_MODULE_VERSION=R4.31-1.0.0
IOCADMIN_MODULE_VERSION=R3.1.15-1.0.0

# ==========================================================
# 定义模块路径 - 请根据您的实际安装路径修改
# ==========================================================

# 选项1: 如果您有标准的 EPICS 模块目录结构
# EPICS_MODULES = /opt/epics/modules
# ASYN = $(EPICS_MODULES)/asyn/$(ASYN_MODULE_VERSION)
# IOCADMIN = $(EPICS_MODULES)/iocAdmin/$(IOCADMIN_MODULE_VERSION)

# 选项2: 使用绝对路径（推荐用于独立编译）
# 请将以下路径修改为您系统中的实际路径
ASYN = /opt/epics/modules/asyn/R4.31-1.0.0
IOCADMIN = /opt/epics/modules/iocAdmin/R3.1.15-1.0.0

# 如果您没有 iocAdmin，可以注释掉上面的行，并在 ipmiIocApp/src/Makefile 中移除相关引用

# ==========================================================
# 设置 EPICS_BASE - 请修改为您的 EPICS Base 安装路径
# ==========================================================
EPICS_BASE = /opt/epics/base

# 如果您有 EPICS_SITE_TOP 设置，可以使用：
# EPICS_BASE = $(EPICS_SITE_TOP)/base/$(BASE_MODULE_VERSION)

