4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 1764
y 267
w 820
h 700
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA $(id) - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 0
w 824
h 40
lineColor index 54
fill
fillColor index 54
lineWidth 0
endObjectProperties

# (Exit Button)
object activeExitButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 720
y 8
w 48
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
label "Exit"
font "helvetica-medium-r-12.0"
3d
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 0
w 264
h 40
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "MicroTCA Module"
  "Cooling Unit - CU $(unit)"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 648
y 680
w 168
h 16
controlPv "SIOC:SYS0:AL00:TOD"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
fontAlign "right"
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 656
y 8
w 56
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "Home..."
numPvs 4
numDsps 1
displayFileName {
  0 "lcls_main"
}
setPosition {
  0 "parentWindow"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 408
w 232
h 272
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 400
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Temperature Monitors"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 8
y 680
w 120
h 16
controlPv "SIOC:SYS0:AL00:MODE"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 440
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 136
y 440
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 136
y 440
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 16
y 440
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 472
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 136
y 472
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 136
y 472
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 16
y 472
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 504
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 136
y 504
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 136
y 504
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 16
y 504
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 536
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 136
y 536
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 136
y 536
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 16
y 536
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 568
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 136
y 568
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 136
y 568
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 16
y 568
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP5P"
visMin "1"
visMax "2"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 256
y 304
w 232
h 376
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 256
y 288
w 88
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Cooling Fans"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 440
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 384
y 440
w 88
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED1"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 384
y 440
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):FANSPEED1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 264
y 440
w 104
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):FANSPEED1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 472
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 384
y 472
w 88
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED2"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 384
y 472
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):FANSPEED2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 264
y 472
w 104
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):FANSPEED2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 504
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 384
y 504
w 88
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED3"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 384
y 504
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):FANSPEED3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 264
y 504
w 104
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):FANSPEED3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 536
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 384
y 536
w 88
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED4"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 384
y 536
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):FANSPEED4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 264
y 536
w 104
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):FANSPEED4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 568
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 384
y 568
w 88
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED5"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 384
y 568
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):FANSPEED5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 264
y 568
w 104
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):FANSPEED5P"
visMin "1"
visMax "2"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 264
y 312
w 160
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "Performs self-adjustment?"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 424
y 312
w 48
h 24
controlPv "$(crat):$(id)$(unit):FANAUTO"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Motif Slider)
object activeMotifSliderClass
beginObjectProperties
major 4
minor 2
release 0
x 264
y 336
w 216
h 56
fgColor index 14
bgColor index 3
2ndBgColor index 5
topShadowColor index 1
botShadowColor index 11
increment 1
controlPv "$(crat):$(id)$(unit):FANSPEEDSET"
controlLabel "Fan  Level:"
font "helvetica-medium-r-12.0"
limitsFromDb
precision 0
showLimits
showLabel
showValue
savedValuePv "$(crat):$(id)$(unit):FANSPEED"
showSavedValue
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 344
y 400
w 48
h 24
controlPv "$(crat):$(id)$(unit):FANLEVEL"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 264
y 400
w 120
h 24
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "Current level:"
}
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 504
y 408
w 232
h 272
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 504
y 400
w 128
h 18
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Voltage Monitors"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 512
y 440
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 632
y 437
w 88
h 26
controlPv "$(crat):$(id)$(unit):V1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 632
y 437
w 88
h 26
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 512
y 437
w 104
h 26
controlPv "$(crat):$(id)$(unit):V1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 512
y 472
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 632
y 470
w 88
h 26
controlPv "$(crat):$(id)$(unit):V2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 632
y 470
w 88
h 26
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 512
y 470
w 104
h 26
controlPv "$(crat):$(id)$(unit):V2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 512
y 512
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 632
y 505
w 88
h 26
controlPv "$(crat):$(id)$(unit):V3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 632
y 505
w 88
h 26
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 512
y 505
w 104
h 26
controlPv "$(crat):$(id)$(unit):V3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 512
y 536
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 632
y 539
w 88
h 26
controlPv "$(crat):$(id)$(unit):V4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 632
y 539
w 88
h 26
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 512
y 539
w 104
h 26
controlPv "$(crat):$(id)$(unit):V4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 512
y 568
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 632
y 572
w 88
h 26
controlPv "$(crat):$(id)$(unit):V5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 632
y 572
w 88
h 26
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 512
y 572
w 104
h 26
controlPv "$(crat):$(id)$(unit):V5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 600
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 384
y 600
w 88
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED6"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 384
y 600
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):FANSPEED6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 264
y 600
w 104
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):FANSPEED6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 576
y 48
w 232
h 320

beginGroup

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 576
y 48
w 232
h 320
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_utca_power_nat"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

endGroup

visPv "$(crat):TYPE"
visMin "27"
visMax "28"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 576
y 48
w 232
h 320

beginGroup

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 576
y 48
w 232
h 320
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_utca_power_vt"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

endGroup

visPv "$(crat):TYPE"
visMin "26"
visMax "27"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 576
y 48
w 232
h 320

beginGroup

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 576
y 48
w 232
h 320
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_atca_power"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

endGroup

visPv "$(crat):TYPE_RAW"
visMin "4"
visMax "6"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 48
w 560
h 224

beginGroup

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 48
w 560
h 224
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_utca_info"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

endGroup

visPv "$(crat):TYPE_RAW"
visMin "1"
visMax "3"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 48
w 560
h 224

beginGroup

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 48
w 560
h 224
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_atca_info"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

endGroup

visPv "$(crat):TYPE_RAW"
visMin "4"
visMax "6"
endObjectProperties

