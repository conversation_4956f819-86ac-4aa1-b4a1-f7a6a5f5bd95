#==============================================================================
#
# Abs:  Populate Supermicro and Advantech server PVs
#
# Name:  server_pc.substitutions
#
# Macros in:
#	dev     Shelf name, for example CRAT:LI28:RF01
#       link    Name of shelf used in asynIPPortConfigure in st.cmd, for example crat-li28-rf01
#	location Building, rack, elevation
#       inpa - inpl PVs to include in alarm summary (.SEVR field of specified PV is used)
#	prefix   String in PV name that identifies the FRU sensor is associated with (if there is one)
#		 PV name syntax: $(dev):$(prefix)$(attr)$(sensinst)
#		 For microTCA systems, prefix is "<frutype><instance>_", for example AMC1_ for the first AMC
#		 or CU2_ for the second cooling unit
#		 For supermicro, the sensors are not associated with a FRU, so prefix is an empty string
#	aliasprefix Temporary string to keep PV names backward-compatible via alias
#	id       String identifier, for example 'CU' for cooling unit
#       unit     Instance of this type of module, for example 2 for the second MCH in a carrier
#       attr	 Used in PV attribute, for example FAN1SPEED or TEMP2
#       sensinst Instance of this type of sensor on this module
#       type     Sensor type code, defined in IPMI spec (and src/ipmiDef.h)
#       fruid    FRU ID according to MicroTCA spec
#
#==============================================================================
#

file system_common.db
{
   pattern { dev	, link		, location	}
	   { $(dev)	, $(link)	, $(location)	}	
}

file system_chassis_status.db
{
   pattern { dev	}
	   { $(dev)	}	
}

file fru_common.db
{
   pattern { dev	, id		, unit		, fruid		}	
           { $(dev)	, FRU		, 0  		, 0		}
}

file sensor_ai.db
{
   pattern { dev	, prefix, noalias , aliasprefix	,  attr		, sensinst	, type	, fruid }
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 1		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 2		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 3		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 4		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 5		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 6		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 7		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 8		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 9		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 10		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 11		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 12		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 13		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 14		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 15		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 16		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 17		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 18		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 19		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  TEMP		, 20		, 1	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 1		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 2		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 3		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 4		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 5		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 6		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 7		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 8		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 9		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 10		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 11		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 12		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 13		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 14		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 15		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 16		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 17		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 18		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 19		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  V		, 20		, 2	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 1		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 2		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 3		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 4		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 5		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 6		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 7		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 8		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 9		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 10		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 11		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 12		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 13		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 14		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 15		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 16		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 17		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 18		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 19		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  I		, 20		, 3	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  FANSPEED	, 1		, 4	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  FANSPEED	, 2		, 4	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  FANSPEED	, 3		, 4	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  FANSPEED	, 4		, 4	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  FANSPEED	, 5		, 4	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  FANSPEED	, 6		, 4	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  FANSPEED	, 7		, 4	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  FANSPEED	, 8		, 4	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  FANSPEED	, 9		, 4	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  FANSPEED	, 10		, 4	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  FANSPEED	, 11		, 4	, 0	}
           { $(dev)	, ""	, "#"     ,  ""		,  FANSPEED	, 12		, 4	, 0	}
}	   		  	
	   		  	
	   		  	
	   		  	
	   		  	
	   		  	
	   		  	
	   		  	
