# RELEASE.local - 示例配置文件
# 根据您的环境修改以下路径

# ==========================================================
# 定义 EPICS 基础路径
# ==========================================================
# 选项1: 使用标准 EPICS 安装
EPICS_BASE = /opt/epics/base

# 选项2: 使用自定义安装路径
# EPICS_BASE = /usr/local/epics/base

# ==========================================================
# 定义模块版本
# ==========================================================
ASYN_MODULE_VERSION = R4.31-1.0.0
IOCADMIN_MODULE_VERSION = R3.1.15-1.0.0

# ==========================================================
# 定义模块路径
# ==========================================================
# 选项1: 使用 EPICS 模块目录结构
# EPICS_MODULES = /opt/epics/modules
# ASYN = $(EPICS_MODULES)/asyn/$(ASYN_MODULE_VERSION)
# IOCADMIN = $(EPICS_MODULES)/iocAdmin/$(IOCADMIN_MODULE_VERSION)

# 选项2: 使用绝对路径
ASYN = /opt/epics/modules/asyn/R4.31-1.0.0
IOCADMIN = /opt/epics/modules/iocAdmin/R3.1.15-1.0.0

# ==========================================================
# 可选：添加其他依赖模块
# ==========================================================
# AUTOSAVE_MODULE_VERSION = R5.8-2.0.0
# AUTOSAVE = $(EPICS_MODULES)/autosave/$(AUTOSAVE_MODULE_VERSION)
