# ipmiComm-v1 编译和使用指南

## 1. 环境要求

### 必需组件
- **EPICS Base**: 版本 3.14.12 或更高
- **asyn 模块**: 版本 R4.31 或兼容版本
- **Linux 系统**: RHEL/CentOS/Ubuntu
- **编译工具**: gcc, make

### 可选组件
- **iocAdmin 模块**: 用于 IOC 状态监控（可选）

## 2. 快速开始

### 2.1 设置环境
```bash
# 1. 设置环境变量
source setup_env.sh

# 2. 或手动设置
export EPICS_BASE=/opt/epics/base
export EPICS_HOST_ARCH=linux-x86_64
```

### 2.2 配置编译
```bash
# 1. 复制并编辑配置文件
cp configure/RELEASE.local.example configure/RELEASE.local

# 2. 编辑 configure/RELEASE.local，设置正确的路径：
# EPICS_BASE = /your/epics/base/path
# ASYN = /your/asyn/module/path
```

### 2.3 编译项目
```bash
# 使用编译脚本（推荐）
chmod +x build.sh
./build.sh

# 或使用传统 make
make clean
make
```

## 3. 详细配置说明

### 3.1 最小配置（仅 EPICS Base + asyn）
如果您只有基本的 EPICS 环境：

```bash
# 使用最小配置
cp configure/RELEASE.minimal configure/RELEASE.local
# 编辑路径后编译
```

### 3.2 完整配置（包含 iocAdmin）
如果您有完整的 EPICS 模块：

```bash
# 编辑 configure/RELEASE.local
# 确保所有模块路径正确
```

## 4. 编译验证

编译成功后，应该生成以下文件：
- `lib/linux-x86_64/libipmiComm.so` - 共享库
- `bin/linux-x86_64/ipmiCommIoc` - IOC 可执行文件
- `dbd/ipmiComm.dbd` - 数据库定义文件

## 5. 运行测试

### 5.1 修改测试配置
```bash
cd iocBoot/sioc-ipmicomm-test
# 编辑 st.cmd，修改设备地址为您的实际设备
```

### 5.2 运行 IOC
```bash
# 在测试目录中
../../bin/linux-x86_64/ipmiCommIoc st.cmd
```

## 6. 常见问题

### 6.1 编译错误
- **找不到 asyn**: 检查 ASYN 路径设置
- **找不到 EPICS_BASE**: 检查 EPICS_BASE 环境变量
- **架构不匹配**: 检查 EPICS_HOST_ARCH 设置

### 6.2 运行时错误
- **网络连接失败**: 检查设备 IP 地址和端口
- **权限问题**: 确保有网络访问权限
- **IPMI 认证失败**: 检查设备 IPMI 配置

## 7. 使用示例

### 7.1 监控 MicroTCA 机箱
```bash
# 在 st.cmd 中配置
drvAsynIPPortConfigure("mch-test", "*************:623 udp", 0, 0, 0)
mchInit("mch-test")
dbLoadRecords("db/shelf_microtca_12slot.db", "dev=CRAT:TEST,link=mch-test,location=Lab1")
```

### 7.2 监控服务器
```bash
# 在 st.cmd 中配置
drvAsynIPPortConfigure("server-test", "*************:623 udp", 0, 0, 0)
mchInit("server-test")
dbLoadRecords("db/server_pc.db", "dev=SERVER:TEST,link=server-test,location=Lab1")
```

## 8. 故障排除

### 8.1 启用调试
```bash
# 在 IOC 中设置调试级别
caput CRAT:TEST:DBG 3  # 高级调试
```

### 8.2 网络诊断
```bash
# 测试 IPMI 连接
ipmitool -I lanplus -H ************* -U admin -P admin chassis status
```

## 9. 进阶配置

### 9.1 自定义传感器扫描周期
```bash
# 在 IOC 启动后
caput CRAT:TEST:SCAN_PERIOD 5  # 5秒扫描一次
```

### 9.2 添加新设备类型
参考 `src/drvMchServerPc.c` 和 `src/drvMchPicmg.c` 的实现方式。
