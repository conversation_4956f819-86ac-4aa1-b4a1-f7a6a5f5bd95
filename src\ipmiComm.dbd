driver(drvMch)
device(ai,CAMAC_IO,devAiMch,"MCHsensor")
device(bo,CAMAC_IO,devBoMch,"MCHsensor")
device(bi,CAMAC_IO,devBiMch,"MCHsensor")
device(mbbi,CAMAC_IO,devMbbiMch,"MCHsensor")
device(mbbo,CAMAC_IO,devMbboMch,"MCHsensor")
device(longin,CAMAC_IO,devLonginMch,"MCHsensor")
device(ai,CAMAC_IO,devAiFru,"FRUinfo")
device(longout,CAMAC_IO,devLongoutFru,"FRUinfo")
device(stringin,CAMAC_IO,devStringinFru,"FRUinfo")
registrar(drvMchRegisterCommands)
registrar(drvMchPicmgRegistrar) 
registrar(drvMchServerPcRegistrar)
function(subMchTypeFacility)