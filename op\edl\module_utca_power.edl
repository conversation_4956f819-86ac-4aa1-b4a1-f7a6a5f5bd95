4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 702
y 440
w 300
h 400
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA MCH - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 8
w 304
h 392
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 0
x 0
y 0
w 128
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Module Power"
}
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 32
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):$(id)$(unit):POWERCTL"
pressValue "0"
onLabel "Power Off"
offLabel "Power Off"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 0
release 0
x 120
y 32
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):$(id)$(unit):POWERCTL"
pressValue "1"
onLabel "Power On"
offLabel "Power On"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

# (Byte)
object ByteClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 72
w 32
h 192
controlPv "$(crat):$(id)$(unit):HOTSWAP1.RVAL"
lineColor index 14
onColor index 25
offColor index 3
endian "little"
numBits 8
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 72
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.ONST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 96
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.TWST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 120
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.THST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 144
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.FRST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 168
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.FVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 192
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.SXST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 240
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.EIST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 216
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.SVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 0
x 112
y 96
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power off)"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 0
x 104
y 168
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power on)"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 152
y 280
w 48
h 24
controlPv "$(crat):$(id)$(unit):PWR"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 280
w 152
h 24
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "Steady-state power draw"
}
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 3
release 0
x 208
y 280
w 24
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "..."
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca_power_detailed"
}
setPosition {
  0 "parentWindow"
}
endObjectProperties

