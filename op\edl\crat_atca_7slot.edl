crat_atca_header.edl                                                                                0000664 0015122 0001750 00000004731 13151345117 013302  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 702
y 328
w 972
h 752
font "helvetica-medium-r-12.0"
ctlFont "helvetica-medium-r-12.0"
btnFont "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 14
ctlFgColor2 index 14
ctlBgColor1 index 4
ctlBgColor2 index 4
topShadowColor index 1
botShadowColor index 11
title "MicroTCA crate - $(crat)"
showGrid
snapToGrid
gridSize 4
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 8
w 448
h 164
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 100
y 32
w 88
h 24
controlPv "$(crat):ONLNSTAT"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 32
w 96
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Network"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 76
w 96
h 20
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Communication"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 0
w 176
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "System Status and Control"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 100
y 76
w 88
h 24
controlPv "$(crat):INIT"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 296
y 76
w 108
h 32
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 0
useDisplayBg
visPv "$(crat):INIT"
visMin "1"
visMax "2"
value {
  "Re-initializing takes"
  "tens of seconds"
}
endObjectProperties

# (Menu Button)
object activeMenuButtonClass
beginObjectProperties
major 4
minor 0
release 0
x 196
y 76
w 92
h 28
fgColor index 14
bgColor index 3
inconsistentColor index 5
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):CONNECT"
font "helvetica-medium-r-10.0"
endObjectProperties

                                       crat_utca_12slot.edl                                                                                0000664 0015122 0001750 00000131666 13151345114 013227  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 1800
y 190
w 972
h 752
font "helvetica-medium-r-12.0"
ctlFont "helvetica-medium-r-12.0"
btnFont "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 14
ctlFgColor2 index 14
ctlBgColor1 index 4
ctlBgColor2 index 4
topShadowColor index 1
botShadowColor index 11
title "MicroTCA crate - $(crat)"
showGrid
snapToGrid
gridSize 4
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 4
y 276
w 956
h 472
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 148
y 492
w 684
h 132
lineColor index 14
fill
fillColor index 6
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 148
y 328
w 680
h 132
lineColor index 14
fill
fillColor index 6
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 0
w 972
h 40
lineColor index 54
fill
fillColor index 54
lineWidth 0
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 0
w 208
h 40
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "MicroTCA Crate "
  "$(crat)"
}
endObjectProperties

# (Exit Button)
object activeExitButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 876
y 8
w 48
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
label "Exit"
font "helvetica-medium-r-12.0"
3d
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 4
y 264
w 176
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Crate Contents "
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 212
y 464
w 44
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 272
y 464
w 40
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "3"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 328
y 464
w 40
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "4"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 380
y 464
w 44
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "5"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 440
y 464
w 40
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "6"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 156
y 464
w 44
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 144
y 304
w 56
h 28
font "helvetica-bold-i-14.0"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Front"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 144
y 620
w 56
h 28
font "helvetica-bold-i-14.0"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Rear"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 156
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 157
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 159
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 92
y 352
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 93
y 352
w 47
h 82
fgColor index 14
bgColor index 72
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=MCH,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 95
y 361
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "MCH "
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):MCH1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 356
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 841
y 356
w 47
h 82
fgColor index 14
bgColor index 72
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=MCH,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 843
y 365
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "MCH "
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):MCH2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 36
y 352
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 37
y 352
w 47
h 82
fgColor index 14
bgColor index 56
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca_pm"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=PM,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 39
y 361
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Power"
  "Module"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):PM1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 896
y 356
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 897
y 356
w 47
h 82
fgColor index 14
bgColor index 56
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca_pm"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=PM,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 899
y 365
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Power"
  "Module"
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):PM2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 900
y 520
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 901
y 519
w 47
h 82
fgColor index 14
bgColor index 56
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca_pm"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=PM,unit=4"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 903
y 527
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Power"
  "Module"
  "4..."
}
endObjectProperties

endGroup

visPv "$(crat):PM4:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 36
y 520
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 37
y 520
w 47
h 82
fgColor index 14
bgColor index 56
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca_pm"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=PM,unit=3"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 39
y 527
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Power"
  "Module"
  "3..."
}
endObjectProperties

endGroup

visPv "$(crat):PM3:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 312
y 284
w 348
h 40

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 319
y 284
w 341
h 36
fgColor index 14
bgColor index 61
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca_cu"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=CU,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 339
y 291
w 293
h 26
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Cooling Unit 2..."
}
endObjectProperties

endGroup

visPv "$(crat):CU2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 212
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 213
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 215
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC"
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 268
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 269
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=3"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 271
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC"
  "3..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC3:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 324
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 325
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=4"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 327
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC"
  "4..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC4:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 380
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 381
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=5"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 383
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC"
  "5..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC5:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 436
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 437
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=6"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 439
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC"
  "6..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC6:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 156
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 157
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 159
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 212
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 213
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 215
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 268
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 269
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=3"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 271
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "3..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM3:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 324
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 325
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=4"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 327
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "4..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM4:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 380
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 381
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=5"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 383
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "5..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM5:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 436
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 437
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=6"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 439
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM "
  "6..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM6:P"
visMin "1"
visMax "2"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 148
y 328
w 60
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 552
y 464
w 40
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "8"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 608
y 464
w 40
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "9"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 660
y 464
w 48
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "10"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 716
y 464
w 48
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "11"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 776
y 464
w 44
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "12"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 496
y 464
w 40
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "7"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 492
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 493
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=7"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 495
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC "
  "7..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC7:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 548
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 549
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=8"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 551
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC "
  "8..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC8:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 604
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 605
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=9"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 607
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC "
  "9..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC9:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 660
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 661
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=10"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 664
y 352
w 41
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC "
  "10..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC10:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 716
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 717
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=11"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 719
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC "
  "11..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC11:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 772
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 773
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=12"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 775
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC "
  "12..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC12:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 492
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 493
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=7"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 495
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "7..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM7:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 548
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 549
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=8"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 551
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "8..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM8:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 604
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 605
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=9"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 607
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "9..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM9:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 660
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 661
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=10"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 663
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "10..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM10:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 716
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 717
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=11"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 719
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "11..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM11:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 772
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 773
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=12"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 775
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "12..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM12:P"
visMin "1"
visMax "2"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 328
w 60
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 712
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 656
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 600
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 544
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 488
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 432
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 320
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 376
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 208
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 148
y 492
w 60
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 492
w 60
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 712
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 656
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 600
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 544
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 488
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 432
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 376
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 320
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 208
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 4
y 56
w 964
h 176
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "crat_utca_header"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 28
y 652
w 64
h 44

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 28
y 652
w 64
h 44
fgColor index 14
bgColor index 50
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=CLK,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 32
y 656
w 56
h 36
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Clock Mod"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):CLK1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 808
y 652
w 64
h 44

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 808
y 652
w 64
h 44
fgColor index 14
bgColor index 50
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=CLK,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 812
y 656
w 56
h 36
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Clock Mod"
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):CLK2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 108
y 652
w 64
h 44

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 108
y 652
w 64
h 44
fgColor index 14
bgColor index 51
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=HUB,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 112
y 656
w 56
h 36
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Hub Mod"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):HUB1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 884
y 652
w 64
h 44

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 884
y 652
w 64
h 44
fgColor index 14
bgColor index 51
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=HUB,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 888
y 656
w 56
h 36
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Hub Mod"
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):HUB2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 92
y 520
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 93
y 520
w 47
h 82
fgColor index 14
bgColor index 70
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=MCHRTM,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 95
y 529
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "MCH "
  "RTM"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):MCHRTM1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 844
y 520
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 845
y 520
w 47
h 82
fgColor index 14
bgColor index 70
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=MCHRTM,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 847
y 529
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "MCH "
  "RTM"
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):MCHRTM2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 184
y 704
w 104
h 36

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 184
y 704
w 104
h 36
fgColor index 14
bgColor index 54
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=SH,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 192
y 708
w 92
h 28
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Shelf Info"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):SH1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 312
y 704
w 104
h 36

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 312
y 704
w 104
h 36
fgColor index 14
bgColor index 54
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=SH,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 320
y 708
w 92
h 28
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Shelf Info"
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):SH2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 444
y 704
w 104
h 36

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 444
y 704
w 104
h 36
fgColor index 14
bgColor index 54
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=MCHCM,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 452
y 708
w 92
h 28
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "MCH Carrier Mgmt"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):SHM1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 572
y 704
w 104
h 36

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 572
y 704
w 104
h 36
fgColor index 14
bgColor index 54
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=SHM,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 580
y 708
w 92
h 28
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Shelf Mgr"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):SHM1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 700
y 704
w 104
h 36

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 700
y 704
w 104
h 36
fgColor index 14
bgColor index 54
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=CM,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 708
y 708
w 92
h 28
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Carrier Mgr"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):CM1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 820
y 704
w 104
h 36

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 820
y 704
w 104
h 36
fgColor index 14
bgColor index 54
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=BP,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 828
y 708
w 92
h 28
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Backplane"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):BP1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 312
y 632
w 348
h 40

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 319
y 632
w 341
h 36
fgColor index 14
bgColor index 61
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca_cu"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=CU,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 339
y 639
w 293
h 26
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Cooling Unit 1..."
}
endObjectProperties

endGroup

visPv "$(crat):CU1:P"
visMin "1"
visMax "2"
endObjectProperties

                                                                          crat_utca_header.edl                                                                                0000664 0015122 0001750 00000006734 13151345114 013330  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 702
y 328
w 972
h 752
font "helvetica-medium-r-12.0"
ctlFont "helvetica-medium-r-12.0"
btnFont "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 14
ctlFgColor2 index 14
ctlBgColor1 index 4
ctlBgColor2 index 4
topShadowColor index 1
botShadowColor index 11
title "MicroTCA crate - $(crat)"
showGrid
snapToGrid
gridSize 4
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 460
y 8
w 392
h 164
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 8
w 448
h 164
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 484
y 12
w 380
h 152
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "AMC    - Advanced Mezzanine Card    - card in front of crate"
  "RTM     - Rear Transition Module          - card in rear of crate"
  "MCH    - MicroTCA Carrier Hub           - crate controller"
  "CU       - Cooling Unit                          - crate fan unit"
  "PM       - Power Module                      - crate power supply"
  "Carrier  - crate"
  "Shelf    - series of connected crates"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 100
y 32
w 88
h 24
controlPv "$(crat):ONLNSTAT"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 32
w 96
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Network"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 76
w 96
h 20
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Communication"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 0
w 176
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "System Status and Control"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 100
y 76
w 88
h 24
controlPv "$(crat):INIT"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 460
y 0
w 100
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Terms"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 296
y 76
w 108
h 32
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 0
useDisplayBg
visPv "$(crat):INIT"
visMin "1"
visMax "2"
value {
  "Re-initializing takes"
  "tens of seconds"
}
endObjectProperties

# (Menu Button)
object activeMenuButtonClass
beginObjectProperties
major 4
minor 0
release 0
x 196
y 76
w 92
h 28
fgColor index 14
bgColor index 3
inconsistentColor index 5
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):CONNECT"
font "helvetica-medium-r-10.0"
endObjectProperties

                                    module_atca_info.edl                                                                                0000664 0015122 0001750 00000011535 13151345117 013341  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 526
y 264
w 600
h 500
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA MCH - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 8
w 584
h 352
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 0
w 96
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Module Info"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 48
w 80
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "Manufacturer"
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 96
y 48
w 216
h 24
controlPv "$(crat):$(id)$(unit):BMANUF"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 96
y 80
w 216
h 24
controlPv "$(crat):$(id)$(unit):BPRODNAME"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 80
w 80
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "Product Name"
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 96
y 112
w 216
h 24
controlPv "$(crat):$(id)$(unit):BSN"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 112
w 80
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "Serial Number"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 144
w 80
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "Part Number"
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 96
y 144
w 216
h 24
controlPv "$(crat):$(id)$(unit):BPARTNUMBER"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 96
y 24
w 216
h 16
font "helvetica-medium-i-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Board"
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 328
y 48
w 216
h 24
controlPv "$(crat):$(id)$(unit):PMANUF"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 328
y 80
w 216
h 24
controlPv "$(crat):$(id)$(unit):PPRODNAME"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 328
y 112
w 216
h 24
controlPv "$(crat):$(id)$(unit):PSN"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 328
y 144
w 216
h 24
controlPv "$(crat):$(id)$(unit):PPARTNUMBER"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 328
y 24
w 216
h 16
font "helvetica-medium-i-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Product"
}
endObjectProperties

                                                                                                                                                                   module_atca_nopowerinfo.edl                                                                         0000664 0015122 0001750 00000130460 13151345117 014752  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 425
y 488
w 804
h 708
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA $(id) - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 0
w 824
h 40
lineColor index 54
fill
fillColor index 54
lineWidth 0
endObjectProperties

# (Exit Button)
object activeExitButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 672
y 8
w 48
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
label "Exit"
font "helvetica-medium-r-12.0"
3d
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 0
w 264
h 40
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "Crate Module"
  "$(crat) $(id) $(unit)"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 632
y 688
w 168
h 16
controlPv "SIOC:SYS0:AL00:TOD"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
fontAlign "right"
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 608
y 8
w 56
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "Home..."
numPvs 4
numDsps 1
displayFileName {
  0 "lcls_main"
}
setPosition {
  0 "parentWindow"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 16
y 688
w 120
h 16
controlPv "SIOC:SYS0:AL00:MODE"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
endObjectProperties

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 56
w 560
h 208
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_atca_info"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 328
w 232
h 352
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 16
y 320
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Temperature Sensors"
}
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 328
w 232
h 352
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 264
y 320
w 128
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Voltages"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 352
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 352
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 352
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 352
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 384
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 384
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 384
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 384
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 416
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 416
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 416
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 416
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 448
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 448
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 448
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 448
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 480
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 480
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 480
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 480
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 352
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 352
w 88
h 24
controlPv "$(crat):$(id)$(unit):V1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 352
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 352
w 104
h 24
controlPv "$(crat):$(id)$(unit):V1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 384
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 384
w 88
h 24
controlPv "$(crat):$(id)$(unit):V2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 384
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 384
w 104
h 24
controlPv "$(crat):$(id)$(unit):V2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 416
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 416
w 88
h 24
controlPv "$(crat):$(id)$(unit):V3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 416
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 416
w 104
h 24
controlPv "$(crat):$(id)$(unit):V3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 448
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 448
w 88
h 24
controlPv "$(crat):$(id)$(unit):V4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 448
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 448
w 104
h 24
controlPv "$(crat):$(id)$(unit):V4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 480
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 480
w 88
h 24
controlPv "$(crat):$(id)$(unit):V5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 480
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 480
w 104
h 24
controlPv "$(crat):$(id)$(unit):V5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 512
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 512
w 88
h 24
controlPv "$(crat):$(id)$(unit):V6"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 512
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 512
w 104
h 24
controlPv "$(crat):$(id)$(unit):V6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 544
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 544
w 88
h 24
controlPv "$(crat):$(id)$(unit):V7"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 544
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 544
w 104
h 24
controlPv "$(crat):$(id)$(unit):V7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 576
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 576
w 88
h 24
controlPv "$(crat):$(id)$(unit):V8"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 576
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 576
w 104
h 24
controlPv "$(crat):$(id)$(unit):V8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 608
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 608
w 88
h 24
controlPv "$(crat):$(id)$(unit):V9"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 608
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 608
w 104
h 24
controlPv "$(crat):$(id)$(unit):V9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V9P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 640
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 640
w 88
h 24
controlPv "$(crat):$(id)$(unit):V10"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 640
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 640
w 104
h 24
controlPv "$(crat):$(id)$(unit):V10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V10P"
visMin "1"
visMax "2"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 512
y 328
w 232
h 352
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 352
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 352
w 91
h 24
controlPv "$(crat):$(id)$(unit):I1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 352
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 352
w 104
h 24
controlPv "$(crat):$(id)$(unit):I1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 384
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 384
w 91
h 24
controlPv "$(crat):$(id)$(unit):I2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 384
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 384
w 104
h 24
controlPv "$(crat):$(id)$(unit):I2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 416
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 416
w 91
h 24
controlPv "$(crat):$(id)$(unit):I3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 416
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 416
w 104
h 24
controlPv "$(crat):$(id)$(unit):I3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 448
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 448
w 91
h 24
controlPv "$(crat):$(id)$(unit):I4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 448
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 448
w 104
h 24
controlPv "$(crat):$(id)$(unit):I4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 480
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 480
w 91
h 24
controlPv "$(crat):$(id)$(unit):I5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 480
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 480
w 104
h 24
controlPv "$(crat):$(id)$(unit):I5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 512
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 512
w 91
h 24
controlPv "$(crat):$(id)$(unit):I6"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 512
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 512
w 104
h 24
controlPv "$(crat):$(id)$(unit):I6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 544
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 544
w 91
h 24
controlPv "$(crat):$(id)$(unit):I7"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 544
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 544
w 104
h 24
controlPv "$(crat):$(id)$(unit):I7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 576
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 576
w 91
h 24
controlPv "$(crat):$(id)$(unit):I8"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 576
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 576
w 104
h 24
controlPv "$(crat):$(id)$(unit):I8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 608
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 608
w 91
h 24
controlPv "$(crat):$(id)$(unit):I9"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 608
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 608
w 104
h 24
controlPv "$(crat):$(id)$(unit):I9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I9P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 640
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 640
w 91
h 24
controlPv "$(crat):$(id)$(unit):I10"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 640
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 640
w 104
h 24
controlPv "$(crat):$(id)$(unit):I10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I10P"
visMin "1"
visMax "2"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 512
y 320
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Current Monitors"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 640
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 640
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP10"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 640
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 640
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP10P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 512
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 512
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP6"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 512
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 512
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 544
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 544
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP7"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 544
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 544
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 576
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 576
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP8"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 576
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 576
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 608
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 608
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP9"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 608
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 608
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP9P"
visMin "1"
visMax "2"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 584
y 64
w 208
h 240
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 584
y 56
w 128
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Module Power"
}
endObjectProperties

# (Byte)
object ByteClass
beginObjectProperties
major 4
minor 0
release 0
x 600
y 96
w 32
h 192
controlPv "$(crat):$(id)$(unit):MSTATE.RVAL"
lineColor index 14
onColor index 25
offColor index 3
endian "little"
numBits 8
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 640
y 96
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.ONST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 640
y 120
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.TWST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 640
y 144
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.THST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 640
y 168
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.FRST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 640
y 192
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.FVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 640
y 216
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.SXST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 640
y 264
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.EIST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 640
y 240
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.SVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 696
y 120
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power off)"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 688
y 192
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power on)"
}
endObjectProperties

                                                                                                                                                                                                                module_atca_power.edl                                                                               0000664 0015122 0001750 00000010212 13151345117 013531  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 654
y 490
w 300
h 400
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA MCH - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 8
w 304
h 392
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 0
w 128
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Module Power"
}
endObjectProperties

# (Byte)
object ByteClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 40
w 32
h 192
controlPv "$(crat):$(id)$(unit):MSTATE.RVAL"
lineColor index 14
onColor index 25
offColor index 3
endian "little"
numBits 8
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 40
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.ONST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 64
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.TWST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 88
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.THST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 112
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.FRST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 136
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.FVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 160
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.SXST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 208
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.EIST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 184
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.SVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 104
y 64
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power off)"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 96
y 136
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power on)"
}
endObjectProperties

                                                                                                                                                                                                                                                                                                                                                                                      module_atca_power_noinfo.edl                                                                        0000664 0015122 0001750 00000010212 13151345117 015101  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 654
y 490
w 300
h 400
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA MCH - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 8
w 304
h 392
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 0
w 128
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Module Power"
}
endObjectProperties

# (Byte)
object ByteClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 40
w 32
h 192
controlPv "$(crat):$(id)$(unit):MSTATE.RVAL"
lineColor index 14
onColor index 25
offColor index 3
endian "little"
numBits 8
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 40
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.ONST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 64
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.TWST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 88
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.THST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 112
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.FRST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 136
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.FVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 160
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.SXST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 208
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.EIST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 48
y 184
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.SVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 104
y 64
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power off)"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 96
y 136
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power on)"
}
endObjectProperties

                                                                                                                                                                                                                                                                                                                                                                                      module_atca_rtm.edl                                                                                 0000664 0015122 0001750 00000054477 13151345117 013224  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 694
y 185
w 820
h 556
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "ATCA $(id) - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 0
w 824
h 40
lineColor index 54
fill
fillColor index 54
lineWidth 0
endObjectProperties

# (Exit Button)
object activeExitButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 728
y 8
w 48
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
label "Exit"
font "helvetica-medium-r-12.0"
3d
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 0
w 264
h 40
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "Crate Module"
  "$(crat) $(id) $(unit)"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 632
y 536
w 168
h 16
controlPv "SIOC:SYS0:AL00:TOD"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
fontAlign "right"
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 664
y 8
w 56
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "Home..."
numPvs 4
numDsps 1
displayFileName {
  0 "lcls_main"
}
setPosition {
  0 "parentWindow"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 16
y 536
w 120
h 16
controlPv "SIOC:SYS0:AL00:MODE"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
endObjectProperties

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 56
w 560
h 224
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_atca_info"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 336
w 232
h 192
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 16
y 328
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Temperature Sensors"
}
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 336
w 232
h 192
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 264
y 328
w 128
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Voltages"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 360
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 360
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 360
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 360
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 392
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 392
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 392
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 392
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 424
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 424
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 424
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 424
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 456
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 456
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 456
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 456
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 488
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 488
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 488
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 488
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 360
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 360
w 88
h 24
controlPv "$(crat):$(id)$(unit):V1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 360
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 360
w 104
h 24
controlPv "$(crat):$(id)$(unit):V1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 392
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 392
w 88
h 24
controlPv "$(crat):$(id)$(unit):V2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 392
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 392
w 104
h 24
controlPv "$(crat):$(id)$(unit):V2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 424
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 424
w 88
h 24
controlPv "$(crat):$(id)$(unit):V3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 424
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 424
w 104
h 24
controlPv "$(crat):$(id)$(unit):V3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 456
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 456
w 88
h 24
controlPv "$(crat):$(id)$(unit):V4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 456
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 456
w 104
h 24
controlPv "$(crat):$(id)$(unit):V4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 488
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 488
w 88
h 24
controlPv "$(crat):$(id)$(unit):V5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 488
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 488
w 104
h 24
controlPv "$(crat):$(id)$(unit):V5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V5P"
visMin "1"
visMax "2"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 512
y 336
w 232
h 192
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 360
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 360
w 91
h 24
controlPv "$(crat):$(id)$(unit):I1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 360
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 360
w 104
h 24
controlPv "$(crat):$(id)$(unit):I1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 392
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 392
w 91
h 24
controlPv "$(crat):$(id)$(unit):I2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 392
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 392
w 104
h 24
controlPv "$(crat):$(id)$(unit):I2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 424
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 424
w 91
h 24
controlPv "$(crat):$(id)$(unit):I3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 424
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 424
w 104
h 24
controlPv "$(crat):$(id)$(unit):I3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 456
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 456
w 91
h 24
controlPv "$(crat):$(id)$(unit):I4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 456
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 456
w 104
h 24
controlPv "$(crat):$(id)$(unit):I4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 488
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 488
w 91
h 24
controlPv "$(crat):$(id)$(unit):I5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 488
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 488
w 104
h 24
controlPv "$(crat):$(id)$(unit):I5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I5P"
visMin "1"
visMax "2"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 512
y 328
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Current Monitors"
}
endObjectProperties

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 576
y 56
w 232
h 256
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_atca_power_noinfo"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

                                                                                                                                                                                                 module_utca_cu.edl                                                                                  0000664 0015122 0001750 00000066227 13151345114 013046  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 1764
y 267
w 820
h 700
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA $(id) - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 0
w 824
h 40
lineColor index 54
fill
fillColor index 54
lineWidth 0
endObjectProperties

# (Exit Button)
object activeExitButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 720
y 8
w 48
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
label "Exit"
font "helvetica-medium-r-12.0"
3d
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 0
w 264
h 40
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "MicroTCA Module"
  "Cooling Unit - CU $(unit)"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 648
y 680
w 168
h 16
controlPv "SIOC:SYS0:AL00:TOD"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
fontAlign "right"
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 656
y 8
w 56
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "Home..."
numPvs 4
numDsps 1
displayFileName {
  0 "lcls_main"
}
setPosition {
  0 "parentWindow"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 408
w 232
h 272
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 400
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Temperature Monitors"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 8
y 680
w 120
h 16
controlPv "SIOC:SYS0:AL00:MODE"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 440
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 136
y 440
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 136
y 440
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 16
y 440
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 472
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 136
y 472
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 136
y 472
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 16
y 472
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 504
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 136
y 504
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 136
y 504
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 16
y 504
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 536
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 136
y 536
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 136
y 536
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 16
y 536
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 568
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 136
y 568
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 136
y 568
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 16
y 568
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP5P"
visMin "1"
visMax "2"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 256
y 304
w 232
h 376
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 256
y 288
w 88
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Cooling Fans"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 440
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 384
y 440
w 88
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED1"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 384
y 440
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):FANSPEED1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 264
y 440
w 104
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):FANSPEED1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 472
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 384
y 472
w 88
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED2"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 384
y 472
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):FANSPEED2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 264
y 472
w 104
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):FANSPEED2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 504
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 384
y 504
w 88
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED3"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 384
y 504
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):FANSPEED3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 264
y 504
w 104
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):FANSPEED3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 536
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 384
y 536
w 88
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED4"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 384
y 536
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):FANSPEED4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 264
y 536
w 104
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):FANSPEED4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 568
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 384
y 568
w 88
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED5"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 384
y 568
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):FANSPEED5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 264
y 568
w 104
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):FANSPEED5P"
visMin "1"
visMax "2"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 264
y 312
w 160
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "Performs self-adjustment?"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 424
y 312
w 48
h 24
controlPv "$(crat):$(id)$(unit):FANAUTO"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Motif Slider)
object activeMotifSliderClass
beginObjectProperties
major 4
minor 2
release 0
x 264
y 336
w 216
h 56
fgColor index 14
bgColor index 3
2ndBgColor index 5
topShadowColor index 1
botShadowColor index 11
increment 1
controlPv "$(crat):$(id)$(unit):FANSPEEDSET"
controlLabel "Fan  Level:"
font "helvetica-medium-r-12.0"
limitsFromDb
precision 0
showLimits
showLabel
showValue
savedValuePv "$(crat):$(id)$(unit):FANSPEED"
showSavedValue
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 344
y 400
w 48
h 24
controlPv "$(crat):$(id)$(unit):FANLEVEL"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 264
y 400
w 120
h 24
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "Current level:"
}
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 504
y 408
w 232
h 272
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 504
y 400
w 128
h 18
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Voltage Monitors"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 512
y 440
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 632
y 437
w 88
h 26
controlPv "$(crat):$(id)$(unit):V1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 632
y 437
w 88
h 26
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 512
y 437
w 104
h 26
controlPv "$(crat):$(id)$(unit):V1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 512
y 472
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 632
y 470
w 88
h 26
controlPv "$(crat):$(id)$(unit):V2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 632
y 470
w 88
h 26
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 512
y 470
w 104
h 26
controlPv "$(crat):$(id)$(unit):V2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 512
y 512
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 632
y 505
w 88
h 26
controlPv "$(crat):$(id)$(unit):V3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 632
y 505
w 88
h 26
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 512
y 505
w 104
h 26
controlPv "$(crat):$(id)$(unit):V3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 512
y 536
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 632
y 539
w 88
h 26
controlPv "$(crat):$(id)$(unit):V4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 632
y 539
w 88
h 26
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 512
y 539
w 104
h 26
controlPv "$(crat):$(id)$(unit):V4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 512
y 568
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 632
y 572
w 88
h 26
controlPv "$(crat):$(id)$(unit):V5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 632
y 572
w 88
h 26
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 512
y 572
w 104
h 26
controlPv "$(crat):$(id)$(unit):V5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 600
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 384
y 600
w 88
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED6"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 384
y 600
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):FANSPEED6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 264
y 600
w 104
h 24
controlPv "$(crat):$(id)$(unit):FANSPEED6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):FANSPEED6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 576
y 48
w 232
h 320

beginGroup

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 576
y 48
w 232
h 320
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_utca_power_nat"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

endGroup

visPv "$(crat):TYPE"
visMin "27"
visMax "28"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 576
y 48
w 232
h 320

beginGroup

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 576
y 48
w 232
h 320
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_utca_power_vt"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

endGroup

visPv "$(crat):TYPE"
visMin "26"
visMax "27"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 576
y 48
w 232
h 320

beginGroup

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 576
y 48
w 232
h 320
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_atca_power"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

endGroup

visPv "$(crat):TYPE_RAW"
visMin "4"
visMax "6"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 48
w 560
h 224

beginGroup

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 48
w 560
h 224
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_utca_info"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

endGroup

visPv "$(crat):TYPE_RAW"
visMin "1"
visMax "3"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 48
w 560
h 224

beginGroup

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 48
w 560
h 224
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_atca_info"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

endGroup

visPv "$(crat):TYPE_RAW"
visMin "4"
visMax "6"
endObjectProperties

                                                                                                                                                                                                                                                                                                                                                                         module_utca_info.edl                                                                                0000664 0015122 0001750 00000012654 13151345114 013365  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 526
y 264
w 600
h 500
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA MCH - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 8
w 584
h 352
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 0
w 96
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Module Info"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 48
w 80
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "Manufacturer"
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 96
y 48
w 216
h 24
controlPv "$(crat):$(id)$(unit):BMANUF"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 96
y 80
w 216
h 24
controlPv "$(crat):$(id)$(unit):BPRODNAME"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 80
w 80
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "Product Name"
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 96
y 112
w 216
h 24
controlPv "$(crat):$(id)$(unit):BSN"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 112
w 80
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "Serial Number"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 144
w 80
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "Part Number"
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 96
y 144
w 216
h 24
controlPv "$(crat):$(id)$(unit):BPARTNUMBER"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 256
y 192
w 96
h 24
controlPv "$(crat):$(id)$(unit):FRUID"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 168
y 192
w 80
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "FRU ID"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 96
y 24
w 216
h 16
font "helvetica-medium-i-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Board"
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 328
y 48
w 216
h 24
controlPv "$(crat):$(id)$(unit):PMANUF"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 328
y 80
w 216
h 24
controlPv "$(crat):$(id)$(unit):PPRODNAME"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 328
y 112
w 216
h 24
controlPv "$(crat):$(id)$(unit):PSN"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 328
y 144
w 216
h 24
controlPv "$(crat):$(id)$(unit):PPARTNUMBER"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 328
y 24
w 216
h 16
font "helvetica-medium-i-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Product"
}
endObjectProperties

                                                                                    module_utca_pm.edl                                                                                  0000664 0015122 0001750 00000235214 13151345114 013045  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 12
y 162
w 1072
h 904
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA $(id) - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 256
y 296
w 272
h 352
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 0
w 1072
h 40
lineColor index 54
fill
fillColor index 54
lineWidth 0
endObjectProperties

# (Exit Button)
object activeExitButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 976
y 8
w 48
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
label "Exit"
font "helvetica-medium-r-12.0"
3d
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 0
w 264
h 40
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "MicroTCA Module"
  "Power Module - $(id) $(unit)"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 896
y 888
w 168
h 16
controlPv "SIOC:SYS0:AL00:TOD"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
fontAlign "right"
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 912
y 8
w 56
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "Home..."
numPvs 4
numDsps 1
displayFileName {
  0 "lcls_main"
}
setPosition {
  0 "parentWindow"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 8
y 888
w 120
h 16
controlPv "SIOC:SYS0:AL00:MODE"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 584
y 56
w 232
h 832
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 584
y 48
w 128
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Voltage Monitors"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 80
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 80
w 88
h 24
controlPv "$(crat):$(id)$(unit):V1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 80
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 80
w 104
h 24
controlPv "$(crat):$(id)$(unit):V1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 112
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 112
w 88
h 24
controlPv "$(crat):$(id)$(unit):V2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 112
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 112
w 104
h 24
controlPv "$(crat):$(id)$(unit):V2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 144
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 144
w 88
h 24
controlPv "$(crat):$(id)$(unit):V3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 144
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 144
w 104
h 24
controlPv "$(crat):$(id)$(unit):V3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 176
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 176
w 88
h 24
controlPv "$(crat):$(id)$(unit):V4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 176
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 176
w 104
h 24
controlPv "$(crat):$(id)$(unit):V4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 208
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 208
w 88
h 24
controlPv "$(crat):$(id)$(unit):V5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 208
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 208
w 104
h 24
controlPv "$(crat):$(id)$(unit):V5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 240
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 240
w 88
h 24
controlPv "$(crat):$(id)$(unit):V6"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 240
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 240
w 104
h 24
controlPv "$(crat):$(id)$(unit):V6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 272
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 272
w 88
h 24
controlPv "$(crat):$(id)$(unit):V7"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 272
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 272
w 104
h 24
controlPv "$(crat):$(id)$(unit):V7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 304
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 304
w 88
h 24
controlPv "$(crat):$(id)$(unit):V8"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 304
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 304
w 104
h 24
controlPv "$(crat):$(id)$(unit):V8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 336
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 336
w 88
h 24
controlPv "$(crat):$(id)$(unit):V9"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 336
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 336
w 104
h 24
controlPv "$(crat):$(id)$(unit):V9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V9P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 400
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 400
w 88
h 24
controlPv "$(crat):$(id)$(unit):V11"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 400
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V11"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 400
w 104
h 24
controlPv "$(crat):$(id)$(unit):V11.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V11P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 432
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 432
w 88
h 24
controlPv "$(crat):$(id)$(unit):V12"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 432
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V12"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 432
w 104
h 24
controlPv "$(crat):$(id)$(unit):V12.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V12P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 464
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 464
w 88
h 24
controlPv "$(crat):$(id)$(unit):V13"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 464
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V13"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 464
w 104
h 24
controlPv "$(crat):$(id)$(unit):V13.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V13P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 496
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 496
w 88
h 24
controlPv "$(crat):$(id)$(unit):V14"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 496
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V14"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 496
w 104
h 24
controlPv "$(crat):$(id)$(unit):V14.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V14P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 528
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 528
w 88
h 24
controlPv "$(crat):$(id)$(unit):V15"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 528
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V15"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 528
w 104
h 24
controlPv "$(crat):$(id)$(unit):V15.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V15P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 560
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 560
w 88
h 24
controlPv "$(crat):$(id)$(unit):V16"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 560
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V16"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 560
w 104
h 24
controlPv "$(crat):$(id)$(unit):V16.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V16P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 624
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 624
w 88
h 24
controlPv "$(crat):$(id)$(unit):V18"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 624
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V18"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 624
w 104
h 24
controlPv "$(crat):$(id)$(unit):V18.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V18P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 656
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 656
w 88
h 24
controlPv "$(crat):$(id)$(unit):V19"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 656
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V19"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 656
w 104
h 24
controlPv "$(crat):$(id)$(unit):V19.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V19P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 368
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 368
w 88
h 24
controlPv "$(crat):$(id)$(unit):V10"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 368
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 368
w 104
h 24
controlPv "$(crat):$(id)$(unit):V10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V10P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 720
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 720
w 88
h 24
controlPv "$(crat):$(id)$(unit):V21"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 720
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V21"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 720
w 104
h 24
controlPv "$(crat):$(id)$(unit):V21.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V21P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 752
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 752
w 88
h 24
controlPv "$(crat):$(id)$(unit):V22"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 752
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V22"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 752
w 104
h 24
controlPv "$(crat):$(id)$(unit):V22.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V22P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 784
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 784
w 88
h 24
controlPv "$(crat):$(id)$(unit):V23"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 784
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V23"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 784
w 104
h 24
controlPv "$(crat):$(id)$(unit):V23.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V23P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 816
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 816
w 88
h 24
controlPv "$(crat):$(id)$(unit):V24"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 816
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V24"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 816
w 104
h 24
controlPv "$(crat):$(id)$(unit):V24.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V24P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 848
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 848
w 88
h 24
controlPv "$(crat):$(id)$(unit):V25"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 848
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V25"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 848
w 104
h 24
controlPv "$(crat):$(id)$(unit):V25.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V25P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 688
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 688
w 88
h 24
controlPv "$(crat):$(id)$(unit):V20"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 688
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V20"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 688
w 104
h 24
controlPv "$(crat):$(id)$(unit):V20.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V20P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 592
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 592
w 88
h 24
controlPv "$(crat):$(id)$(unit):V17"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 592
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V17"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 592
w 104
h 24
controlPv "$(crat):$(id)$(unit):V17.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V17P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 312
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 312
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 312
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 312
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 344
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 344
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 344
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 344
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 376
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 376
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 376
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 376
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 408
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 408
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 408
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 408
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 440
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 440
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 440
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 440
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP5P"
visMin "1"
visMax "2"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 256
y 288
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Temperature Monitors"
}
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 832
y 56
w 232
h 832
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 832
y 48
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Current Monitors"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 80
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 80
w 91
h 24
controlPv "$(crat):$(id)$(unit):I1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 80
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 80
w 104
h 24
controlPv "$(crat):$(id)$(unit):I1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 112
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 112
w 91
h 24
controlPv "$(crat):$(id)$(unit):I2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 112
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 112
w 104
h 24
controlPv "$(crat):$(id)$(unit):I2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 144
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 144
w 91
h 24
controlPv "$(crat):$(id)$(unit):I3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 144
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 144
w 104
h 24
controlPv "$(crat):$(id)$(unit):I3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 176
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 176
w 91
h 24
controlPv "$(crat):$(id)$(unit):I4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 176
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 176
w 104
h 24
controlPv "$(crat):$(id)$(unit):I4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 208
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 208
w 91
h 24
controlPv "$(crat):$(id)$(unit):I5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 208
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 208
w 104
h 24
controlPv "$(crat):$(id)$(unit):I5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 240
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 240
w 91
h 24
controlPv "$(crat):$(id)$(unit):I6"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 240
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 240
w 104
h 24
controlPv "$(crat):$(id)$(unit):I6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 272
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 272
w 91
h 24
controlPv "$(crat):$(id)$(unit):I7"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 272
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 272
w 104
h 24
controlPv "$(crat):$(id)$(unit):I7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 304
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 304
w 91
h 24
controlPv "$(crat):$(id)$(unit):I8"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 304
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 304
w 104
h 24
controlPv "$(crat):$(id)$(unit):I8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 336
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 336
w 91
h 24
controlPv "$(crat):$(id)$(unit):I9"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 336
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 336
w 104
h 24
controlPv "$(crat):$(id)$(unit):I9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I9P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 400
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 400
w 91
h 24
controlPv "$(crat):$(id)$(unit):I11"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 400
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I11"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 400
w 104
h 24
controlPv "$(crat):$(id)$(unit):I11.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I11P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 432
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 432
w 91
h 24
controlPv "$(crat):$(id)$(unit):I12"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 432
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I12"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 432
w 104
h 24
controlPv "$(crat):$(id)$(unit):I12.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I12P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 464
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 464
w 91
h 24
controlPv "$(crat):$(id)$(unit):I13"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 464
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I13"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 464
w 104
h 24
controlPv "$(crat):$(id)$(unit):I13.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I13P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 496
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 496
w 91
h 24
controlPv "$(crat):$(id)$(unit):I14"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 496
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I14"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 496
w 104
h 24
controlPv "$(crat):$(id)$(unit):I14.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I14P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 528
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 528
w 91
h 24
controlPv "$(crat):$(id)$(unit):I15"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 528
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I15"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 528
w 104
h 24
controlPv "$(crat):$(id)$(unit):I15.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I15P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 560
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 560
w 91
h 24
controlPv "$(crat):$(id)$(unit):I16"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 560
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I16"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 560
w 104
h 24
controlPv "$(crat):$(id)$(unit):I16.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I16P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 592
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 592
w 91
h 24
controlPv "$(crat):$(id)$(unit):I17"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 592
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I17"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 592
w 104
h 24
controlPv "$(crat):$(id)$(unit):I17.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I17P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 624
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 624
w 91
h 24
controlPv "$(crat):$(id)$(unit):I18"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 624
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I18"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 624
w 104
h 24
controlPv "$(crat):$(id)$(unit):I8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I18P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 656
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 656
w 91
h 24
controlPv "$(crat):$(id)$(unit):I19"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 656
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I19"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 656
w 104
h 24
controlPv "$(crat):$(id)$(unit):I19.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I19P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 368
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 368
w 91
h 24
controlPv "$(crat):$(id)$(unit):I10"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 368
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 368
w 104
h 24
controlPv "$(crat):$(id)$(unit):I10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I10P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 720
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 720
w 91
h 24
controlPv "$(crat):$(id)$(unit):I21"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 720
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I21"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 720
w 104
h 24
controlPv "$(crat):$(id)$(unit):I21.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I21P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 752
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 752
w 91
h 24
controlPv "$(crat):$(id)$(unit):I22"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 752
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I22"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 752
w 104
h 24
controlPv "$(crat):$(id)$(unit):I22.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I22P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 784
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 784
w 91
h 24
controlPv "$(crat):$(id)$(unit):I23"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 784
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I23"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 784
w 104
h 24
controlPv "$(crat):$(id)$(unit):I23.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I23P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 816
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 816
w 91
h 24
controlPv "$(crat):$(id)$(unit):I24"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 816
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I24"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 816
w 104
h 24
controlPv "$(crat):$(id)$(unit):I24.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I24P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 848
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 848
w 91
h 24
controlPv "$(crat):$(id)$(unit):I25"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 848
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I25"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 848
w 104
h 24
controlPv "$(crat):$(id)$(unit):I25.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I25P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 688
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 688
w 91
h 24
controlPv "$(crat):$(id)$(unit):I20"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 688
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I20"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 688
w 104
h 24
controlPv "$(crat):$(id)$(unit):I20.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I20P"
visMin "1"
visMax "2"
endObjectProperties

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 48
w 560
h 224
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_utca_info"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 288
w 232
h 320

beginGroup

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 288
w 232
h 320
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_utca_power_nat"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

endGroup

visPv "$(crat):TYPE"
visMin "27"
visMax "28"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 288
w 232
h 320

beginGroup

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 288
w 232
h 320
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_utca_power_vt"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

endGroup

visPv "$(crat):TYPE"
visMin "26"
visMax "27"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 600
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 600
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP10"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 600
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 600
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP10P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 472
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 472
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP6"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 472
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 472
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 504
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 504
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP7"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 504
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 504
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 536
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 536
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP8"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 536
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 536
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 568
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 568
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP9"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 568
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 568
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP9P"
visMin "1"
visMax "2"
endObjectProperties

                                                                                                                                                                                                                                                                                                                                                                                    module_utca_power.edl                                                                               0000664 0015122 0001750 00000013451 13151345114 013562  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 702
y 440
w 300
h 400
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA MCH - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 8
w 304
h 392
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 0
x 0
y 0
w 128
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Module Power"
}
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 32
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):$(id)$(unit):POWERCTL"
pressValue "0"
onLabel "Power Off"
offLabel "Power Off"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 0
release 0
x 120
y 32
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):$(id)$(unit):POWERCTL"
pressValue "1"
onLabel "Power On"
offLabel "Power On"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

# (Byte)
object ByteClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 72
w 32
h 192
controlPv "$(crat):$(id)$(unit):HOTSWAP1.RVAL"
lineColor index 14
onColor index 25
offColor index 3
endian "little"
numBits 8
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 72
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.ONST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 96
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.TWST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 120
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.THST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 144
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.FRST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 168
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.FVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 192
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.SXST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 240
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.EIST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 56
y 216
w 152
h 24
controlPv "$(crat):$(id)$(unit):HOTSWAP1.SVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 0
x 112
y 96
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power off)"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 0
x 104
y 168
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power on)"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 152
y 280
w 48
h 24
controlPv "$(crat):$(id)$(unit):PWR"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 280
w 152
h 24
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "Steady-state power draw"
}
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 3
release 0
x 208
y 280
w 24
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "..."
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca_power_detailed"
}
setPosition {
  0 "parentWindow"
}
endObjectProperties

                                                                                                                                                                                                                       module_utca_power_detailed.edl                                                                      0000664 0015122 0001750 00000014773 13151345114 015425  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 645
y 297
w 616
h 300
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA $(id) - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 0
w 616
h 40
lineColor index 54
fill
fillColor index 54
lineWidth 0
endObjectProperties

# (Exit Button)
object activeExitButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 528
y 8
w 48
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
label "Exit"
font "helvetica-medium-r-12.0"
3d
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 0
w 264
h 40
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "MicroTCA Module - Power Details"
  "$(id) $(unit)"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 440
y 288
w 168
h 16
controlPv "SIOC:SYS0:AL00:TOD"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
fontAlign "right"
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 3
release 0
x 464
y 8
w 56
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "Home..."
numPvs 4
numDsps 1
displayFileName {
  0 "lcls_main"
}
setPosition {
  0 "parentWindow"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 8
y 288
w 120
h 16
controlPv "SIOC:SYS0:AL00:MODE"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 56
w 600
h 232
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 48
w 176
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Power Consumption"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 0
x 40
y 88
w 232
h 24
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "Supports dynamic power configuration?"
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 40
y 120
w 216
h 24
controlPv "$(crat):$(id)$(unit):PWR.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Control)
object activeXTextDspClass
beginObjectProperties
major 4
minor 5
release 0
x 280
y 120
w 88
h 24
controlPv "$(crat):$(id)$(unit):PWR"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
showUnits
useAlarmBorder
newPos
objType "controls"
endObjectProperties

# (Text Control)
object activeXTextDspClass
beginObjectProperties
major 4
minor 5
release 0
x 280
y 152
w 88
h 24
controlPv "$(crat):$(id)$(unit):PWRDES"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
showUnits
useAlarmBorder
newPos
objType "controls"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 40
y 152
w 216
h 24
controlPv "$(crat):$(id)$(unit):PWRDES.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 40
y 184
w 216
h 24
controlPv "$(crat):$(id)$(unit):EPWR.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Control)
object activeXTextDspClass
beginObjectProperties
major 4
minor 5
release 0
x 280
y 184
w 88
h 24
controlPv "$(crat):$(id)$(unit):EPWR"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
showUnits
useAlarmBorder
newPos
objType "controls"
endObjectProperties

# (Text Control)
object activeXTextDspClass
beginObjectProperties
major 4
minor 5
release 0
x 280
y 216
w 88
h 24
controlPv "$(crat):$(id)$(unit):EPWRDES"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
showUnits
useAlarmBorder
newPos
objType "controls"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 40
y 216
w 216
h 24
controlPv "$(crat):$(id)$(unit):EPWRDES.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Control)
object activeXTextDspClass
beginObjectProperties
major 4
minor 5
release 0
x 280
y 88
w 88
h 24
controlPv "$(crat):$(id)$(unit):PWRDYN"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
showUnits
useAlarmBorder
newPos
objType "controls"
endObjectProperties

# (Text Control)
object activeXTextDspClass
beginObjectProperties
major 4
minor 5
release 0
x 280
y 248
w 88
h 24
controlPv "$(crat):$(id)$(unit):PWRDLY"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
showUnits
useAlarmBorder
newPos
objType "controls"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 40
y 248
w 216
h 24
controlPv "$(crat):$(id)$(unit):PWRDLY.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

     module_utca_power_nat.edl                                                                           0000664 0015122 0001750 00000011616 13151345114 014425  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 808
y 250
w 300
h 400
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA MCH - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 8
w 304
h 400
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 0
w 128
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Module Power"
}
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 16
y 32
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):$(id)$(unit):POWERCTL"
pressValue "0"
onLabel "Deactivate"
offLabel "Deactivate"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 120
y 32
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):$(id)$(unit):POWERCTL"
pressValue "1"
onLabel "Activate"
offLabel "Activate"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

# (Byte)
object ByteClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 72
w 32
h 192
controlPv "$(crat):$(id)$(unit):MSTATE.RVAL"
lineColor index 14
onColor index 25
offColor index 3
endian "little"
numBits 8
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 72
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.ONST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 96
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.TWST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 120
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.THST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 144
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.FRST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 168
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.FVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 192
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.SXST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 240
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.EIST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 216
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.SVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 112
y 96
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power off)"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 104
y 168
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power on)"
}
endObjectProperties

                                                                                                                  module_utca_power_vt.edl                                                                            0000664 0015122 0001750 00000013431 13151345114 014271  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 654
y 490
w 300
h 400
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA MCH - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 8
w 304
h 392
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 0
w 128
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Module Power"
}
endObjectProperties

# (Byte)
object ByteClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 72
w 32
h 192
controlPv "$(crat):$(id)$(unit):MSTATE.RVAL"
lineColor index 14
onColor index 25
offColor index 3
endian "little"
numBits 8
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 72
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.ONST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 96
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.TWST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 120
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.THST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 144
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.FRST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 168
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.FVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 192
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.SXST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 240
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.EIST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 216
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.SVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 112
y 96
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power off)"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 104
y 168
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power on)"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 152
y 280
w 48
h 24
controlPv "$(crat):$(id)$(unit):PWR"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 280
w 152
h 24
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "Steady-state power draw"
}
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 208
y 280
w 24
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "..."
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca_power_detailed"
}
setPosition {
  0 "parentWindow"
}
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 120
y 32
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):$(id)$(unit):POWERCTL"
pressValue "1"
onLabel "Activate"
offLabel "Activate"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 16
y 32
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):$(id)$(unit):POWERCTL"
pressValue "0"
onLabel "Deactivate"
offLabel "Deactivate"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

                                                                                                                                                                                                                                       server_pc.edl                                                                                       0000664 0015122 0001750 00000161324 13151345131 012037  0                                                                                                    ustar   sonya                           ad                                                                                                                                                                                                                     4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 152
y 126
w 1008
h 700
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "Server - $(crat)"
showGrid
snapToGrid
gridSize 4
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 0
w 1008
h 40
lineColor index 54
fill
fillColor index 54
lineWidth 0
endObjectProperties

# (Exit Button)
object activeExitButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 912
y 8
w 48
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
label "Exit"
font "helvetica-medium-r-12.0"
3d
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 16
w 152
h 28
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "$(crat)"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 824
y 672
w 168
h 16
controlPv "SIOC:SYS0:AL00:TOD"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
fontAlign "right"
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 848
y 8
w 56
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "Home..."
numPvs 4
numDsps 1
displayFileName {
  0 "lcls_main"
}
setPosition {
  0 "parentWindow"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 16
y 672
w 120
h 16
controlPv "SIOC:SYS0:AL00:MODE"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
endObjectProperties

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 432
y 56
w 560
h 224
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "menu"
filePv "LOC\\cratPV=1"
sizeOfs 5
numDsps 1
displayFileName {
  0 "module_utca_info"
}
symbols {
  0 "id=FRU,unit=0"
}
noScroll
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 312
w 232
h 352
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 16
y 304
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Temperature Sensors"
}
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 312
w 232
h 352
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 264
y 304
w 128
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Voltages"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 336
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 336
w 91
h 24
controlPv "$(crat):TEMP1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 336
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 336
w 104
h 24
controlPv "$(crat):TEMP1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 368
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 368
w 91
h 24
controlPv "$(crat):TEMP2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 368
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 368
w 104
h 24
controlPv "$(crat):TEMP2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 400
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 400
w 91
h 24
controlPv "$(crat):TEMP3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 400
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 400
w 104
h 24
controlPv "$(crat):TEMP3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 432
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 432
w 91
h 24
controlPv "$(crat):TEMP4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 432
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 432
w 104
h 24
controlPv "$(crat):TEMP4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 464
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 464
w 91
h 24
controlPv "$(crat):TEMP5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 464
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 464
w 104
h 24
controlPv "$(crat):TEMP5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 336
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 336
w 88
h 24
controlPv "$(crat):V1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 336
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 336
w 104
h 24
controlPv "$(crat):V1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 368
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 368
w 88
h 24
controlPv "$(crat):V2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 368
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 368
w 104
h 24
controlPv "$(crat):V2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 400
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 400
w 88
h 24
controlPv "$(crat):V3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 400
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 400
w 104
h 24
controlPv "$(crat):V3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 432
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 432
w 88
h 24
controlPv "$(crat):V4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 432
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 432
w 104
h 24
controlPv "$(crat):V4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 464
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 464
w 88
h 24
controlPv "$(crat):V5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 464
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 464
w 104
h 24
controlPv "$(crat):V5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 496
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 496
w 88
h 24
controlPv "$(crat):V6"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 496
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 496
w 104
h 24
controlPv "$(crat):V6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 528
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 528
w 88
h 24
controlPv "$(crat):V7"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 528
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 528
w 104
h 24
controlPv "$(crat):V7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 560
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 560
w 88
h 24
controlPv "$(crat):V8"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 560
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 560
w 104
h 24
controlPv "$(crat):V8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 592
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 592
w 88
h 24
controlPv "$(crat):V9"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 592
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 592
w 104
h 24
controlPv "$(crat):V9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V9P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 624
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 624
w 88
h 24
controlPv "$(crat):V10"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 624
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 624
w 104
h 24
controlPv "$(crat):V10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V10P"
visMin "1"
visMax "2"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 512
y 312
w 232
h 352
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 336
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 336
w 91
h 24
controlPv "$(crat):I1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 336
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 336
w 104
h 24
controlPv "$(crat):I1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 368
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 368
w 91
h 24
controlPv "$(crat):I2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 368
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 368
w 104
h 24
controlPv "$(crat):I2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 400
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 400
w 91
h 24
controlPv "$(crat):I3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 400
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 400
w 104
h 24
controlPv "$(crat):I3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 432
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 432
w 91
h 24
controlPv "$(crat):I4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 432
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 432
w 104
h 24
controlPv "$(crat):I4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 464
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 464
w 91
h 24
controlPv "$(crat):I5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 464
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 464
w 104
h 24
controlPv "$(crat):I5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 496
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 496
w 91
h 24
controlPv "$(crat):I6"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 496
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 496
w 104
h 24
controlPv "$(crat):I6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 528
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 528
w 91
h 24
controlPv "$(crat):I7"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 528
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 528
w 104
h 24
controlPv "$(crat):I7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 560
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 560
w 91
h 24
controlPv "$(crat):I8"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 560
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 560
w 104
h 24
controlPv "$(crat):I8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 592
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 592
w 91
h 24
controlPv "$(crat):I9"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 592
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 592
w 104
h 24
controlPv "$(crat):I9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I9P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 624
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 624
w 91
h 24
controlPv "$(crat):I10"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 624
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 624
w 104
h 24
controlPv "$(crat):I10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I10P"
visMin "1"
visMax "2"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 512
y 304
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Current Monitors"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 624
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 624
w 91
h 24
controlPv "$(crat):TEMP10"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 624
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 624
w 104
h 24
controlPv "$(crat):TEMP10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP10P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 496
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 496
w 91
h 24
controlPv "$(crat):TEMP6"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 496
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 496
w 104
h 24
controlPv "$(crat):TEMP6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 528
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 528
w 91
h 24
controlPv "$(crat):TEMP7"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 528
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 528
w 104
h 24
controlPv "$(crat):TEMP7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 560
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 560
w 91
h 24
controlPv "$(crat):TEMP8"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 560
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 560
w 104
h 24
controlPv "$(crat):TEMP8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 592
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 592
w 91
h 24
controlPv "$(crat):TEMP9"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 592
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 592
w 104
h 24
controlPv "$(crat):TEMP9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP9P"
visMin "1"
visMax "2"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 760
y 320
w 232
h 344
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 760
y 304
w 88
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Cooling Fans"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 336
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 336
w 88
h 24
controlPv "$(crat):FANSPEED1"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 336
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 336
w 104
h 24
controlPv "$(crat):FANSPEED1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 368
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 368
w 88
h 24
controlPv "$(crat):FANSPEED2"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 368
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 368
w 104
h 24
controlPv "$(crat):FANSPEED2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 400
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 400
w 88
h 24
controlPv "$(crat):FANSPEED3"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 400
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 400
w 104
h 24
controlPv "$(crat):FANSPEED3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 432
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 432
w 88
h 24
controlPv "$(crat):FANSPEED4"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 432
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 432
w 104
h 24
controlPv "$(crat):FANSPEED4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 464
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 464
w 88
h 24
controlPv "$(crat):FANSPEED5"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 464
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 464
w 104
h 24
controlPv "$(crat):FANSPEED5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 496
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 496
w 88
h 24
controlPv "$(crat):FANSPEED6"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 496
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 496
w 104
h 24
controlPv "$(crat):FANSPEED6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 528
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 528
w 88
h 24
controlPv "$(crat):FANSPEED7"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 528
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 528
w 104
h 24
controlPv "$(crat):FANSPEED7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 560
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 560
w 88
h 24
controlPv "$(crat):FANSPEED8"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 560
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 560
w 104
h 24
controlPv "$(crat):FANSPEED8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 592
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 592
w 88
h 24
controlPv "$(crat):FANSPEED9"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 592
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 592
w 104
h 24
controlPv "$(crat):FANSPEED9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED9P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 624
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 624
w 88
h 24
controlPv "$(crat):FANSPEED10"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 624
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 624
w 104
h 24
controlPv "$(crat):FANSPEED10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED10P"
visMin "1"
visMax "2"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 156
y 24
w 160
h 16
controlPv "$(crat):LOC"
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 64
w 416
h 216
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 112
y 88
w 88
h 24
controlPv "$(crat):ONLNSTAT"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 88
w 96
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Network"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 128
w 96
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Communication"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 56
w 176
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "System Status and Control"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 112
y 128
w 88
h 24
controlPv "$(crat):INIT"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 308
y 128
w 108
h 32
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 0
useDisplayBg
visPv "$(crat):INIT"
visMin "1"
visMax "2"
value {
  "Re-initializing takes"
  "tens of seconds"
}
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 24
y 216
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):POWEROFF"
pressValue "0"
onLabel "Power Off"
offLabel "Power Off"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 120
y 216
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):POWERON"
pressValue "1"
onLabel "Power On"
offLabel "Power On"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 216
y 216
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):HARDRESET"
pressValue "3"
onLabel "Hard Reset"
offLabel "Hard Reset"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 112
y 168
w 88
h 24
controlPv "$(crat):POWERSTATE"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 288
y 256
w 48
h 20
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "Info..."
numPvs 4
numDsps 1
displayFileName {
  0 "crat_ipmi_chassis_details"
}
setPosition {
  0 "parentWindow"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 168
w 96
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Chassis Power"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 208
y 168
w 96
h 24
controlPv "$(crat):POWERMSG"
fgColor index 14
bgColor index 12
font "helvetica-medium-r-10.0"
fontAlign "center"
endObjectProperties

# (Menu Button)
object activeMenuButtonClass
beginObjectProperties
major 4
minor 0
release 0
x 208
y 128
w 92
h 28
fgColor index 14
fgAlarm
bgColor index 3
inconsistentColor index 5
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):CONNECT"
font "helvetica-medium-r-10.0"
colorPv "$(crat):CONNECT"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 8
y 4
w 160
h 16
controlPv "$(crat):TYPE_RAW"
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            