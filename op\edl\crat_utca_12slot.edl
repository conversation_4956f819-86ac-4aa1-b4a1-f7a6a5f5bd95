4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 1800
y 190
w 972
h 752
font "helvetica-medium-r-12.0"
ctlFont "helvetica-medium-r-12.0"
btnFont "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 14
ctlFgColor2 index 14
ctlBgColor1 index 4
ctlBgColor2 index 4
topShadowColor index 1
botShadowColor index 11
title "MicroTCA crate - $(crat)"
showGrid
snapToGrid
gridSize 4
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 4
y 276
w 956
h 472
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 148
y 492
w 684
h 132
lineColor index 14
fill
fillColor index 6
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 148
y 328
w 680
h 132
lineColor index 14
fill
fillColor index 6
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 0
w 972
h 40
lineColor index 54
fill
fillColor index 54
lineWidth 0
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 0
w 208
h 40
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "MicroTCA Crate "
  "$(crat)"
}
endObjectProperties

# (Exit Button)
object activeExitButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 876
y 8
w 48
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
label "Exit"
font "helvetica-medium-r-12.0"
3d
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 4
y 264
w 176
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Crate Contents "
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 212
y 464
w 44
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 272
y 464
w 40
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "3"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 328
y 464
w 40
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "4"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 380
y 464
w 44
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "5"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 440
y 464
w 40
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "6"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 156
y 464
w 44
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 144
y 304
w 56
h 28
font "helvetica-bold-i-14.0"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Front"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 144
y 620
w 56
h 28
font "helvetica-bold-i-14.0"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Rear"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 156
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 157
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 159
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 92
y 352
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 93
y 352
w 47
h 82
fgColor index 14
bgColor index 72
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=MCH,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 95
y 361
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "MCH "
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):MCH1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 356
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 841
y 356
w 47
h 82
fgColor index 14
bgColor index 72
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=MCH,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 843
y 365
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "MCH "
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):MCH2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 36
y 352
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 37
y 352
w 47
h 82
fgColor index 14
bgColor index 56
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca_pm"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=PM,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 39
y 361
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Power"
  "Module"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):PM1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 896
y 356
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 897
y 356
w 47
h 82
fgColor index 14
bgColor index 56
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca_pm"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=PM,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 899
y 365
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Power"
  "Module"
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):PM2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 900
y 520
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 901
y 519
w 47
h 82
fgColor index 14
bgColor index 56
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca_pm"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=PM,unit=4"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 903
y 527
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Power"
  "Module"
  "4..."
}
endObjectProperties

endGroup

visPv "$(crat):PM4:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 36
y 520
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 37
y 520
w 47
h 82
fgColor index 14
bgColor index 56
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca_pm"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=PM,unit=3"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 39
y 527
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Power"
  "Module"
  "3..."
}
endObjectProperties

endGroup

visPv "$(crat):PM3:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 312
y 284
w 348
h 40

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 319
y 284
w 341
h 36
fgColor index 14
bgColor index 61
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca_cu"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=CU,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 339
y 291
w 293
h 26
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Cooling Unit 2..."
}
endObjectProperties

endGroup

visPv "$(crat):CU2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 212
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 213
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 215
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC"
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 268
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 269
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=3"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 271
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC"
  "3..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC3:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 324
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 325
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=4"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 327
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC"
  "4..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC4:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 380
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 381
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=5"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 383
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC"
  "5..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC5:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 436
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 437
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=6"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 439
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC"
  "6..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC6:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 156
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 157
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 159
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 212
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 213
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 215
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 268
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 269
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=3"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 271
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "3..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM3:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 324
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 325
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=4"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 327
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "4..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM4:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 380
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 381
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=5"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 383
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "5..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM5:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 436
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 437
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=6"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 439
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM "
  "6..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM6:P"
visMin "1"
visMax "2"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 148
y 328
w 60
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 552
y 464
w 40
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "8"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 608
y 464
w 40
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "9"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 660
y 464
w 48
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "10"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 716
y 464
w 48
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "11"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 776
y 464
w 44
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "12"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 496
y 464
w 40
h 24
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "7"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 492
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 493
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=7"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 495
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC "
  "7..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC7:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 548
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 549
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=8"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 551
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC "
  "8..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC8:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 604
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 605
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=9"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 607
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC "
  "9..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC9:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 660
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 661
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=10"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 664
y 352
w 41
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC "
  "10..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC10:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 716
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 717
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=11"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 719
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC "
  "11..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC11:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 772
y 340
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 773
y 340
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=AMC,unit=12"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 775
y 352
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "AMC "
  "12..."
}
endObjectProperties

endGroup

visPv "$(crat):AMC12:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 492
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 493
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=7"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 495
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "7..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM7:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 548
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 549
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=8"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 551
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "8..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM8:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 604
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 605
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=9"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 607
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "9..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM9:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 660
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 661
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=10"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 663
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "10..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM10:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 716
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 717
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=11"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 719
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "11..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM11:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 772
y 504
w 48
h 108

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 773
y 504
w 47
h 108
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=RTM,unit=12"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 775
y 516
w 40
h 40
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "RTM"
  "12..."
}
endObjectProperties

endGroup

visPv "$(crat):RTM12:P"
visMin "1"
visMax "2"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 328
w 60
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 712
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 656
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 600
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 544
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 488
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 432
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 320
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 376
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 208
y 328
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 148
y 492
w 60
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 492
w 60
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 712
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 656
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 600
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 544
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 488
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 432
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 376
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 320
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 208
y 492
w 56
h 132
lineColor index 14
fillColor index 4
endObjectProperties

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 4
y 56
w 964
h 176
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "crat_utca_header"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 28
y 652
w 64
h 44

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 28
y 652
w 64
h 44
fgColor index 14
bgColor index 50
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=CLK,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 32
y 656
w 56
h 36
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Clock Mod"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):CLK1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 808
y 652
w 64
h 44

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 808
y 652
w 64
h 44
fgColor index 14
bgColor index 50
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=CLK,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 812
y 656
w 56
h 36
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Clock Mod"
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):CLK2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 108
y 652
w 64
h 44

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 108
y 652
w 64
h 44
fgColor index 14
bgColor index 51
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=HUB,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 112
y 656
w 56
h 36
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Hub Mod"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):HUB1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 884
y 652
w 64
h 44

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 884
y 652
w 64
h 44
fgColor index 14
bgColor index 51
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=HUB,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 888
y 656
w 56
h 36
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Hub Mod"
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):HUB2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 92
y 520
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 93
y 520
w 47
h 82
fgColor index 14
bgColor index 70
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=MCHRTM,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 95
y 529
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "MCH "
  "RTM"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):MCHRTM1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 844
y 520
w 48
h 84

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 845
y 520
w 47
h 82
fgColor index 14
bgColor index 70
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=MCHRTM,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 847
y 529
w 40
h 37
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "MCH "
  "RTM"
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):MCHRTM2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 184
y 704
w 104
h 36

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 184
y 704
w 104
h 36
fgColor index 14
bgColor index 54
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=SH,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 192
y 708
w 92
h 28
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Shelf Info"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):SH1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 312
y 704
w 104
h 36

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 312
y 704
w 104
h 36
fgColor index 14
bgColor index 54
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=SH,unit=2"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 320
y 708
w 92
h 28
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Shelf Info"
  "2..."
}
endObjectProperties

endGroup

visPv "$(crat):SH2:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 444
y 704
w 104
h 36

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 444
y 704
w 104
h 36
fgColor index 14
bgColor index 54
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=MCHCM,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 452
y 708
w 92
h 28
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "MCH Carrier Mgmt"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):SHM1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 572
y 704
w 104
h 36

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 572
y 704
w 104
h 36
fgColor index 14
bgColor index 54
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=SHM,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 580
y 708
w 92
h 28
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Shelf Mgr"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):SHM1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 700
y 704
w 104
h 36

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 700
y 704
w 104
h 36
fgColor index 14
bgColor index 54
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=CM,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 708
y 708
w 92
h 28
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Carrier Mgr"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):CM1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 820
y 704
w 104
h 36

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 820
y 704
w 104
h 36
fgColor index 14
bgColor index 54
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=BP,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 828
y 708
w 92
h 28
font "helvetica-medium-r-10.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Backplane"
  "1..."
}
endObjectProperties

endGroup

visPv "$(crat):BP1:P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 312
y 632
w 348
h 40

beginGroup

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 319
y 632
w 341
h 36
fgColor index 14
bgColor index 61
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
numPvs 4
numDsps 1
displayFileName {
  0 "module_utca_cu"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "id=CU,unit=1"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 339
y 639
w 293
h 26
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 4
useDisplayBg
value {
  "Cooling Unit 1..."
}
endObjectProperties

endGroup

visPv "$(crat):CU1:P"
visMin "1"
visMax "2"
endObjectProperties

