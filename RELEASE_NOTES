R4.2.0:		  25-Oct-2018 <PERSON><PERSON>
		  - Update to handle latest Advantech firmware
		  - Misc fixes and minor changes, see ChangeLog for details

R4.1.0:		  15-Nov-2017 <PERSON><PERSON>adio
		  - Modified to build for git workflow and EPICS 3.15.

ipmiComm-R4-0-2:  10-Aug-2017 <PERSON><PERSON>
		  Fix handling of session sequence numbers when they are greater than expected.
   		    (Should fix problem of not successfully re-connecting to ATCA crates.)
		  ATCA: Associate AMC sensors with Front Board (carrier).
		  Other changes described in Changelog.

ipmiComm-R4-0-1:  14-Jun-2017 <PERSON><PERSON>
		  (See Changelog for details)
		  Fix 'green' sensor records when system offline
		  Add ability to override sensor scan period for special cases/problematic systems
		  Fix ATCA db file names
		  Add iocAdmin to configure/RELEASE_SPEAR

ipmiComm-R4-0-0:  02-Jun-2017 S. Hoobler
		  N.B: 
		       * Not tested with Vadatech MicroTCA MCH *
		       * Workaround in place for Advantech non-increasing sequence numbers *
		  Many changes:
		  Add support for ATCA and Advantech.
		  Move architecture/vendor-specific functionality to callbacks.
		  Changes to memory allocation.
		  Create facility (LCLS) specific database files to be consistent with IOCManager.

ipmiComm-R3-0-3:  11-Oct-2016 <PERSON><PERSON>
		  Revert to asyn-R4-21_1-0 to be consistent with IOCManager

ipmiComm-R3-0-2:  28-May-2016 S. Hoobler
		  Only scan sensors when 'present'. Change sensor scan from
		  10 second scan to i/o event triggered by drvMch.c: mchPing thread

ipmiComm-R3-0-1:  16-May-2016 S. Hoobler
		  configure/RELEASE: Use asyn-R4-25_1-0
		  src/drvMch.c: Restrict debug verbosity on startup to only during 
			initial connection and identifying device

ipmiComm-R3-0-0:  18-Apr-2016 S. Hoobler
		  Add support for Supermicro
                  Refactoring and reorgnization
		  *Not tested with Vadatech MCH*
		  *New PV names but aliases added for backward compatibility"

ipmiComm-R2-0-4:  22-Feb-2016 S. Hoobler
                  Change asyn commands to fix memory leak (ipmiMsg.c)
                  Add iocAdmin

ipmiComm-R2-0-3:  04-Nov-2014 S. Hoobler
		  Add support to read NAT hot swap sensors
		  Start support to get sensor thresholds and push to PV limits
                  (left inactive until further testing complete)
		  Read IPMI version of device
		  Add databases for more FRU types
		  Decode data format for sensor values and limits and then convert			

ipmiComm-R2-0-2:  17-Apr-2014   K. Luchini
                  Upgrade asyn from R4-18-lcls1 to R4-21_1-0

ipmiComm-R2-0-1:  Copy base RULES.Db patch to Makefile/Db so build works on all
                    systems. Changes to support SPEAR and to build in RHEL64. 
                    Add PV archiving intructions.
                    See Changelog for details

ipmiComm-R2-0-0:  Major changes, mostly to handle dynamic configuration changes
                    See Changelog for details

ipmiComm-R1-6-0:  Significantly reduce MCH init time by eliminating read 
                    timeouts where possible. Abort mchSdrGetDataAll for
                    excessive read errors.
                  
ipmiComm-R1-5-1:  Use manufacturer ID to distinguish NAT and Vadatech MCHs
                  No longer implement hot swap sensors for NAT, as they
                    are not implemented consistently among modules
		  See Changelog for more detail

ipmiComm-R1-5-0:  Many changes to support NAT MCH (in addition to Vadatech)
                    See Changelog for more detail

ipmiComm-R1-4-1:  Check sensor 'scanning enabled' before copying
                    sensor reading to PV. Changes to src/devMch.c and src/ipmiDef.c

ipmiComm-R1-4-0:  Add PICMG Get Power Level message and related EPICS records
                  If system was offline, set flag to "not initialized" 
                    (sensor addresses can change with no HW change)
                  Misc changes other changes; see Changelog for details

ipmiComm-R1-3-3:  Check sensor 'reading/state available' before copying
                    sensor reading to PV. Changes to src/devMch.c and src/ipmiDef.c

ipmiComm-R1-3-2:  Set message seq number in request and verify response contains same number
                    Changes to src/drvMch.c and src/ipmiMsg.c
                  Handle error returned from SDR read
                    Change to src/drvMch.c

ipmiComm-R1-3-1:  Fix FRU board and part serial number data type: should be string instead of number
                    Changes to Db/module.db and src/devMch.c

ipmiComm-R1-3-0:  Use "initialized" flag to reflect whether our saved system 
                  configuration matches the actual configuration. If we detect that 
                  the system has just come back online, check hw configuration.
                  If there have been any changes, set flag to "not initialized". 
                  Misc other changes; see Changelog.

ipmiComm-R1-2-0:  Many changes. See Changelog for more details.
                  Determine and specify expected response msg lengths to avoid timeouts, 
                  where possible.
                  Keep track of incoming msg sequence numbers.
                  Set errors based on seq numbers, completion code, asyn status.
                  Add PV and device support to enable/disable communication with MCH.
                  Various fixes, clean-up.

ipmiComm-R1-1-0:  Add support for fan level readback, control, and to read fan properties
                  Additional minor cleanup/changes
                  
ipmiComm-R1-0-0:  Initial release. Built against EPICS base-R3-14-12

                  
