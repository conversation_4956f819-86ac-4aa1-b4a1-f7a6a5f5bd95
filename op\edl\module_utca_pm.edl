4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 12
y 162
w 1072
h 904
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA $(id) - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 256
y 296
w 272
h 352
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 0
w 1072
h 40
lineColor index 54
fill
fillColor index 54
lineWidth 0
endObjectProperties

# (Exit Button)
object activeExitButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 976
y 8
w 48
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
label "Exit"
font "helvetica-medium-r-12.0"
3d
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 0
w 264
h 40
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "MicroTCA Module"
  "Power Module - $(id) $(unit)"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 896
y 888
w 168
h 16
controlPv "SIOC:SYS0:AL00:TOD"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
fontAlign "right"
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 912
y 8
w 56
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "Home..."
numPvs 4
numDsps 1
displayFileName {
  0 "lcls_main"
}
setPosition {
  0 "parentWindow"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 8
y 888
w 120
h 16
controlPv "SIOC:SYS0:AL00:MODE"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 584
y 56
w 232
h 832
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 584
y 48
w 128
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Voltage Monitors"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 80
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 80
w 88
h 24
controlPv "$(crat):$(id)$(unit):V1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 80
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 80
w 104
h 24
controlPv "$(crat):$(id)$(unit):V1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 112
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 112
w 88
h 24
controlPv "$(crat):$(id)$(unit):V2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 112
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 112
w 104
h 24
controlPv "$(crat):$(id)$(unit):V2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 144
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 144
w 88
h 24
controlPv "$(crat):$(id)$(unit):V3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 144
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 144
w 104
h 24
controlPv "$(crat):$(id)$(unit):V3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 176
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 176
w 88
h 24
controlPv "$(crat):$(id)$(unit):V4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 176
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 176
w 104
h 24
controlPv "$(crat):$(id)$(unit):V4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 208
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 208
w 88
h 24
controlPv "$(crat):$(id)$(unit):V5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 208
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 208
w 104
h 24
controlPv "$(crat):$(id)$(unit):V5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 240
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 240
w 88
h 24
controlPv "$(crat):$(id)$(unit):V6"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 240
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 240
w 104
h 24
controlPv "$(crat):$(id)$(unit):V6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 272
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 272
w 88
h 24
controlPv "$(crat):$(id)$(unit):V7"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 272
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 272
w 104
h 24
controlPv "$(crat):$(id)$(unit):V7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 304
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 304
w 88
h 24
controlPv "$(crat):$(id)$(unit):V8"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 304
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 304
w 104
h 24
controlPv "$(crat):$(id)$(unit):V8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 336
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 336
w 88
h 24
controlPv "$(crat):$(id)$(unit):V9"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 336
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 336
w 104
h 24
controlPv "$(crat):$(id)$(unit):V9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V9P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 400
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 400
w 88
h 24
controlPv "$(crat):$(id)$(unit):V11"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 400
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V11"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 400
w 104
h 24
controlPv "$(crat):$(id)$(unit):V11.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V11P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 432
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 432
w 88
h 24
controlPv "$(crat):$(id)$(unit):V12"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 432
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V12"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 432
w 104
h 24
controlPv "$(crat):$(id)$(unit):V12.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V12P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 464
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 464
w 88
h 24
controlPv "$(crat):$(id)$(unit):V13"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 464
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V13"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 464
w 104
h 24
controlPv "$(crat):$(id)$(unit):V13.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V13P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 496
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 496
w 88
h 24
controlPv "$(crat):$(id)$(unit):V14"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 496
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V14"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 496
w 104
h 24
controlPv "$(crat):$(id)$(unit):V14.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V14P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 528
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 528
w 88
h 24
controlPv "$(crat):$(id)$(unit):V15"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 528
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V15"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 528
w 104
h 24
controlPv "$(crat):$(id)$(unit):V15.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V15P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 560
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 560
w 88
h 24
controlPv "$(crat):$(id)$(unit):V16"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 560
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V16"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 560
w 104
h 24
controlPv "$(crat):$(id)$(unit):V16.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V16P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 624
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 624
w 88
h 24
controlPv "$(crat):$(id)$(unit):V18"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 624
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V18"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 624
w 104
h 24
controlPv "$(crat):$(id)$(unit):V18.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V18P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 656
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 656
w 88
h 24
controlPv "$(crat):$(id)$(unit):V19"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 656
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V19"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 656
w 104
h 24
controlPv "$(crat):$(id)$(unit):V19.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V19P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 368
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 368
w 88
h 24
controlPv "$(crat):$(id)$(unit):V10"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 368
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 368
w 104
h 24
controlPv "$(crat):$(id)$(unit):V10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V10P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 720
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 720
w 88
h 24
controlPv "$(crat):$(id)$(unit):V21"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 720
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V21"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 720
w 104
h 24
controlPv "$(crat):$(id)$(unit):V21.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V21P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 752
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 752
w 88
h 24
controlPv "$(crat):$(id)$(unit):V22"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 752
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V22"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 752
w 104
h 24
controlPv "$(crat):$(id)$(unit):V22.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V22P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 784
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 784
w 88
h 24
controlPv "$(crat):$(id)$(unit):V23"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 784
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V23"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 784
w 104
h 24
controlPv "$(crat):$(id)$(unit):V23.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V23P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 816
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 816
w 88
h 24
controlPv "$(crat):$(id)$(unit):V24"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 816
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V24"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 816
w 104
h 24
controlPv "$(crat):$(id)$(unit):V24.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V24P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 848
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 848
w 88
h 24
controlPv "$(crat):$(id)$(unit):V25"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 848
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V25"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 848
w 104
h 24
controlPv "$(crat):$(id)$(unit):V25.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V25P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 688
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 688
w 88
h 24
controlPv "$(crat):$(id)$(unit):V20"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 688
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V20"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 688
w 104
h 24
controlPv "$(crat):$(id)$(unit):V20.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V20P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 592
y 592
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 712
y 592
w 88
h 24
controlPv "$(crat):$(id)$(unit):V17"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 712
y 592
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V17"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 592
y 592
w 104
h 24
controlPv "$(crat):$(id)$(unit):V17.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V17P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 312
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 312
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 312
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 312
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 344
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 344
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 344
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 344
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 376
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 376
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 376
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 376
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 408
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 408
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 408
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 408
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 440
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 440
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 440
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 440
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP5P"
visMin "1"
visMax "2"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 256
y 288
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Temperature Monitors"
}
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 832
y 56
w 232
h 832
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 832
y 48
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Current Monitors"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 80
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 80
w 91
h 24
controlPv "$(crat):$(id)$(unit):I1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 80
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 80
w 104
h 24
controlPv "$(crat):$(id)$(unit):I1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 112
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 112
w 91
h 24
controlPv "$(crat):$(id)$(unit):I2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 112
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 112
w 104
h 24
controlPv "$(crat):$(id)$(unit):I2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 144
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 144
w 91
h 24
controlPv "$(crat):$(id)$(unit):I3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 144
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 144
w 104
h 24
controlPv "$(crat):$(id)$(unit):I3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 176
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 176
w 91
h 24
controlPv "$(crat):$(id)$(unit):I4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 176
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 176
w 104
h 24
controlPv "$(crat):$(id)$(unit):I4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 208
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 208
w 91
h 24
controlPv "$(crat):$(id)$(unit):I5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 208
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 208
w 104
h 24
controlPv "$(crat):$(id)$(unit):I5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 240
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 240
w 91
h 24
controlPv "$(crat):$(id)$(unit):I6"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 240
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 240
w 104
h 24
controlPv "$(crat):$(id)$(unit):I6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 272
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 272
w 91
h 24
controlPv "$(crat):$(id)$(unit):I7"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 272
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 272
w 104
h 24
controlPv "$(crat):$(id)$(unit):I7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 304
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 304
w 91
h 24
controlPv "$(crat):$(id)$(unit):I8"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 304
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 304
w 104
h 24
controlPv "$(crat):$(id)$(unit):I8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 336
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 336
w 91
h 24
controlPv "$(crat):$(id)$(unit):I9"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 336
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 336
w 104
h 24
controlPv "$(crat):$(id)$(unit):I9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I9P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 400
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 400
w 91
h 24
controlPv "$(crat):$(id)$(unit):I11"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 400
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I11"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 400
w 104
h 24
controlPv "$(crat):$(id)$(unit):I11.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I11P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 432
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 432
w 91
h 24
controlPv "$(crat):$(id)$(unit):I12"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 432
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I12"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 432
w 104
h 24
controlPv "$(crat):$(id)$(unit):I12.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I12P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 464
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 464
w 91
h 24
controlPv "$(crat):$(id)$(unit):I13"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 464
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I13"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 464
w 104
h 24
controlPv "$(crat):$(id)$(unit):I13.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I13P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 496
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 496
w 91
h 24
controlPv "$(crat):$(id)$(unit):I14"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 496
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I14"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 496
w 104
h 24
controlPv "$(crat):$(id)$(unit):I14.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I14P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 528
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 528
w 91
h 24
controlPv "$(crat):$(id)$(unit):I15"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 528
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I15"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 528
w 104
h 24
controlPv "$(crat):$(id)$(unit):I15.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I15P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 560
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 560
w 91
h 24
controlPv "$(crat):$(id)$(unit):I16"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 560
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I16"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 560
w 104
h 24
controlPv "$(crat):$(id)$(unit):I16.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I16P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 592
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 592
w 91
h 24
controlPv "$(crat):$(id)$(unit):I17"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 592
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I17"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 592
w 104
h 24
controlPv "$(crat):$(id)$(unit):I17.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I17P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 624
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 624
w 91
h 24
controlPv "$(crat):$(id)$(unit):I18"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 624
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I18"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 624
w 104
h 24
controlPv "$(crat):$(id)$(unit):I8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I18P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 656
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 656
w 91
h 24
controlPv "$(crat):$(id)$(unit):I19"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 656
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I19"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 656
w 104
h 24
controlPv "$(crat):$(id)$(unit):I19.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I19P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 368
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 368
w 91
h 24
controlPv "$(crat):$(id)$(unit):I10"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 368
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 368
w 104
h 24
controlPv "$(crat):$(id)$(unit):I10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I10P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 720
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 720
w 91
h 24
controlPv "$(crat):$(id)$(unit):I21"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 720
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I21"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 720
w 104
h 24
controlPv "$(crat):$(id)$(unit):I21.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I21P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 752
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 752
w 91
h 24
controlPv "$(crat):$(id)$(unit):I22"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 752
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I22"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 752
w 104
h 24
controlPv "$(crat):$(id)$(unit):I22.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I22P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 784
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 784
w 91
h 24
controlPv "$(crat):$(id)$(unit):I23"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 784
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I23"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 784
w 104
h 24
controlPv "$(crat):$(id)$(unit):I23.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I23P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 816
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 816
w 91
h 24
controlPv "$(crat):$(id)$(unit):I24"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 816
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I24"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 816
w 104
h 24
controlPv "$(crat):$(id)$(unit):I24.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I24P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 848
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 848
w 91
h 24
controlPv "$(crat):$(id)$(unit):I25"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 848
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I25"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 848
w 104
h 24
controlPv "$(crat):$(id)$(unit):I25.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I25P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 840
y 688
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 960
y 688
w 91
h 24
controlPv "$(crat):$(id)$(unit):I20"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 960
y 688
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I20"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 840
y 688
w 104
h 24
controlPv "$(crat):$(id)$(unit):I20.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I20P"
visMin "1"
visMax "2"
endObjectProperties

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 48
w 560
h 224
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_utca_info"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 288
w 232
h 320

beginGroup

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 288
w 232
h 320
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_utca_power_nat"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

endGroup

visPv "$(crat):TYPE"
visMin "27"
visMax "28"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 288
w 232
h 320

beginGroup

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 288
w 232
h 320
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_utca_power_vt"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

endGroup

visPv "$(crat):TYPE"
visMin "26"
visMax "27"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 600
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 600
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP10"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 600
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 600
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP10P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 472
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 472
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP6"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 472
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 472
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 504
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 504
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP7"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 504
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 504
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 536
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 536
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP8"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 536
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 536
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 296
y 568
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 416
y 568
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP9"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 416
y 568
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 296
y 568
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP9P"
visMin "1"
visMax "2"
endObjectProperties

