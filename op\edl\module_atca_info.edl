4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 526
y 264
w 600
h 500
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA MCH - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 8
w 584
h 352
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 0
w 96
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Module Info"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 48
w 80
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "Manufacturer"
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 96
y 48
w 216
h 24
controlPv "$(crat):$(id)$(unit):BMANUF"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 96
y 80
w 216
h 24
controlPv "$(crat):$(id)$(unit):BPRODNAME"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 80
w 80
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "Product Name"
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 96
y 112
w 216
h 24
controlPv "$(crat):$(id)$(unit):BSN"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 112
w 80
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "Serial Number"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 144
w 80
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "Part Number"
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 96
y 144
w 216
h 24
controlPv "$(crat):$(id)$(unit):BPARTNUMBER"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 96
y 24
w 216
h 16
font "helvetica-medium-i-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Board"
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 328
y 48
w 216
h 24
controlPv "$(crat):$(id)$(unit):PMANUF"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 328
y 80
w 216
h 24
controlPv "$(crat):$(id)$(unit):PPRODNAME"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 328
y 112
w 216
h 24
controlPv "$(crat):$(id)$(unit):PSN"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 328
y 144
w 216
h 24
controlPv "$(crat):$(id)$(unit):PPARTNUMBER"
font "helvetica-medium-r-12.0"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 328
y 24
w 216
h 16
font "helvetica-medium-i-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Product"
}
endObjectProperties

