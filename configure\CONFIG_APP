# CONFIG_APP

include $(TOP)/configure/RELEASE
-include $(TOP)/configure/RELEASE.$(EPICS_HOST_ARCH)
-include $(TOP)/configure/RELEASE.Common.$(T_A)
-include $(TOP)/configure/RELEASE.$(EPICS_HOST_ARCH).$(T_A)

CONFIG=$(EPICS_BASE)/configure
include $(CONFIG)/CONFIG

INSTALL_LOCATION = $(TOP)
ifdef INSTALL_LOCATION_APP
INSTALL_LOCATION = $(INSTALL_LOCATION_APP)
endif

ifdef T_A
-include $(TOP)/configure/O.$(T_A)/CONFIG_APP_INCLUDE
endif

# dbst based database optimization (default: NO)
DB_OPT = NO
