#!/bin/bash

# ipmiComm-v1 编译脚本
# 使用方法: ./build.sh [clean|rebuild|install]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    print_info "检查编译环境..."
    
    # 检查 EPICS_BASE
    if [ -z "$EPICS_BASE" ]; then
        if [ -f "configure/RELEASE.local" ]; then
            print_info "从 configure/RELEASE.local 读取 EPICS_BASE"
        else
            print_error "EPICS_BASE 未设置，请设置环境变量或创建 configure/RELEASE.local"
            exit 1
        fi
    else
        print_info "EPICS_BASE = $EPICS_BASE"
    fi
    
    # 检查必要的工具
    if ! command -v make &> /dev/null; then
        print_error "make 命令未找到"
        exit 1
    fi
    
    if ! command -v gcc &> /dev/null; then
        print_error "gcc 编译器未找到"
        exit 1
    fi
    
    print_info "环境检查完成"
}

# 清理编译文件
clean_build() {
    print_info "清理编译文件..."
    make clean uninstall
    print_info "清理完成"
}

# 编译项目
build_project() {
    print_info "开始编译 ipmiComm..."
    
    # 编译库
    print_info "编译 ipmiComm 库..."
    make -j$(nproc)
    
    print_info "编译完成"
}

# 安装项目
install_project() {
    print_info "安装项目文件..."
    make install
    print_info "安装完成"
}

# 验证编译结果
verify_build() {
    print_info "验证编译结果..."
    
    # 检查库文件
    if [ -f "lib/linux-x86_64/libipmiComm.so" ] || [ -f "lib/linux-x86_64/libipmiComm.a" ]; then
        print_info "✓ ipmiComm 库编译成功"
    else
        print_warning "ipmiComm 库文件未找到"
    fi
    
    # 检查 IOC 可执行文件
    if [ -f "bin/linux-x86_64/ipmiCommIoc" ]; then
        print_info "✓ ipmiCommIoc 可执行文件编译成功"
    else
        print_warning "ipmiCommIoc 可执行文件未找到"
    fi
    
    # 检查数据库文件
    if [ -f "dbd/ipmiComm.dbd" ]; then
        print_info "✓ 数据库定义文件生成成功"
    else
        print_warning "数据库定义文件未找到"
    fi
}

# 主函数
main() {
    print_info "ipmiComm-v1 编译脚本"
    print_info "========================"
    
    case "${1:-build}" in
        "clean")
            check_environment
            clean_build
            ;;
        "rebuild")
            check_environment
            clean_build
            build_project
            verify_build
            ;;
        "install")
            check_environment
            build_project
            install_project
            verify_build
            ;;
        "build"|"")
            check_environment
            build_project
            verify_build
            ;;
        *)
            print_error "未知参数: $1"
            echo "用法: $0 [clean|rebuild|install|build]"
            exit 1
            ;;
    esac
    
    print_info "操作完成!"
}

# 运行主函数
main "$@"
