4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 645
y 297
w 616
h 300
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA $(id) - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 0
w 616
h 40
lineColor index 54
fill
fillColor index 54
lineWidth 0
endObjectProperties

# (Exit Button)
object activeExitButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 528
y 8
w 48
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
label "Exit"
font "helvetica-medium-r-12.0"
3d
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 0
w 264
h 40
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "MicroTCA Module - Power Details"
  "$(id) $(unit)"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 440
y 288
w 168
h 16
controlPv "SIOC:SYS0:AL00:TOD"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
fontAlign "right"
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 3
release 0
x 464
y 8
w 56
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "Home..."
numPvs 4
numDsps 1
displayFileName {
  0 "lcls_main"
}
setPosition {
  0 "parentWindow"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 8
y 288
w 120
h 16
controlPv "SIOC:SYS0:AL00:MODE"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 56
w 600
h 232
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 48
w 176
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Power Consumption"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 0
x 40
y 88
w 232
h 24
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "Supports dynamic power configuration?"
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 40
y 120
w 216
h 24
controlPv "$(crat):$(id)$(unit):PWR.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Control)
object activeXTextDspClass
beginObjectProperties
major 4
minor 5
release 0
x 280
y 120
w 88
h 24
controlPv "$(crat):$(id)$(unit):PWR"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
showUnits
useAlarmBorder
newPos
objType "controls"
endObjectProperties

# (Text Control)
object activeXTextDspClass
beginObjectProperties
major 4
minor 5
release 0
x 280
y 152
w 88
h 24
controlPv "$(crat):$(id)$(unit):PWRDES"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
showUnits
useAlarmBorder
newPos
objType "controls"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 40
y 152
w 216
h 24
controlPv "$(crat):$(id)$(unit):PWRDES.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 40
y 184
w 216
h 24
controlPv "$(crat):$(id)$(unit):EPWR.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Control)
object activeXTextDspClass
beginObjectProperties
major 4
minor 5
release 0
x 280
y 184
w 88
h 24
controlPv "$(crat):$(id)$(unit):EPWR"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
showUnits
useAlarmBorder
newPos
objType "controls"
endObjectProperties

# (Text Control)
object activeXTextDspClass
beginObjectProperties
major 4
minor 5
release 0
x 280
y 216
w 88
h 24
controlPv "$(crat):$(id)$(unit):EPWRDES"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
showUnits
useAlarmBorder
newPos
objType "controls"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 40
y 216
w 216
h 24
controlPv "$(crat):$(id)$(unit):EPWRDES.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Control)
object activeXTextDspClass
beginObjectProperties
major 4
minor 5
release 0
x 280
y 88
w 88
h 24
controlPv "$(crat):$(id)$(unit):PWRDYN"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
showUnits
useAlarmBorder
newPos
objType "controls"
endObjectProperties

# (Text Control)
object activeXTextDspClass
beginObjectProperties
major 4
minor 5
release 0
x 280
y 248
w 88
h 24
controlPv "$(crat):$(id)$(unit):PWRDLY"
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 16
fgAlarm
bgColor index 12
limitsFromDb
nullColor index 18
useHexPrefix
showUnits
useAlarmBorder
newPos
objType "controls"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 5
release 0
x 40
y 248
w 216
h 24
controlPv "$(crat):$(id)$(unit):PWRDLY.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

