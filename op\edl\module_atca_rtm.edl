4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 694
y 185
w 820
h 556
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "ATCA $(id) - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 0
w 824
h 40
lineColor index 54
fill
fillColor index 54
lineWidth 0
endObjectProperties

# (Exit Button)
object activeExitButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 728
y 8
w 48
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
label "Exit"
font "helvetica-medium-r-12.0"
3d
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 0
w 264
h 40
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "Crate Module"
  "$(crat) $(id) $(unit)"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 632
y 536
w 168
h 16
controlPv "SIOC:SYS0:AL00:TOD"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
fontAlign "right"
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 664
y 8
w 56
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "Home..."
numPvs 4
numDsps 1
displayFileName {
  0 "lcls_main"
}
setPosition {
  0 "parentWindow"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 16
y 536
w 120
h 16
controlPv "SIOC:SYS0:AL00:MODE"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
endObjectProperties

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 8
y 56
w 560
h 224
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_atca_info"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 336
w 232
h 192
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 16
y 328
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Temperature Sensors"
}
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 336
w 232
h 192
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 264
y 328
w 128
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Voltages"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 360
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 360
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 360
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 360
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 392
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 392
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 392
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 392
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 424
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 424
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 424
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 424
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 456
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 456
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 456
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 456
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 488
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 488
w 91
h 24
controlPv "$(crat):$(id)$(unit):TEMP5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 488
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):TEMP5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 488
w 104
h 24
controlPv "$(crat):$(id)$(unit):TEMP5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):TEMP5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 360
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 360
w 88
h 24
controlPv "$(crat):$(id)$(unit):V1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 360
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 360
w 104
h 24
controlPv "$(crat):$(id)$(unit):V1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 392
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 392
w 88
h 24
controlPv "$(crat):$(id)$(unit):V2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 392
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 392
w 104
h 24
controlPv "$(crat):$(id)$(unit):V2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 424
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 424
w 88
h 24
controlPv "$(crat):$(id)$(unit):V3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 424
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 424
w 104
h 24
controlPv "$(crat):$(id)$(unit):V3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 456
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 456
w 88
h 24
controlPv "$(crat):$(id)$(unit):V4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 456
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 456
w 104
h 24
controlPv "$(crat):$(id)$(unit):V4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 488
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 488
w 88
h 24
controlPv "$(crat):$(id)$(unit):V5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 488
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):V5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 488
w 104
h 24
controlPv "$(crat):$(id)$(unit):V5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):V5P"
visMin "1"
visMax "2"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 512
y 336
w 232
h 192
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 360
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 360
w 91
h 24
controlPv "$(crat):$(id)$(unit):I1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 360
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 360
w 104
h 24
controlPv "$(crat):$(id)$(unit):I1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 392
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 392
w 91
h 24
controlPv "$(crat):$(id)$(unit):I2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 392
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 392
w 104
h 24
controlPv "$(crat):$(id)$(unit):I2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 424
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 424
w 91
h 24
controlPv "$(crat):$(id)$(unit):I3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 424
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 424
w 104
h 24
controlPv "$(crat):$(id)$(unit):I3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 456
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 456
w 91
h 24
controlPv "$(crat):$(id)$(unit):I4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 456
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 456
w 104
h 24
controlPv "$(crat):$(id)$(unit):I4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 488
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 488
w 91
h 24
controlPv "$(crat):$(id)$(unit):I5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 488
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):$(id)$(unit):I5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 488
w 104
h 24
controlPv "$(crat):$(id)$(unit):I5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):$(id)$(unit):I5P"
visMin "1"
visMax "2"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 512
y 328
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Current Monitors"
}
endObjectProperties

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 576
y 56
w 232
h 256
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "file"
file "module_atca_power_noinfo"
sizeOfs 5
numDsps 0
noScroll
endObjectProperties

