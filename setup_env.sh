#!/bin/bash

# ipmiComm-v1 环境设置脚本
# 使用方法: source setup_env.sh

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
export IPMICOMM_TOP="$SCRIPT_DIR"

print_info "设置 ipmiComm-v1 环境..."
print_info "IPMICOMM_TOP = $IPMICOMM_TOP"

# 设置 EPICS 环境（如果未设置）
if [ -z "$EPICS_BASE" ]; then
    # 尝试常见的 EPICS 安装路径
    POSSIBLE_EPICS_PATHS=(
        "/opt/epics/base"
        "/usr/local/epics/base"
        "/home/<USER>/epics/base"
        "$HOME/epics/base"
    )
    
    for path in "${POSSIBLE_EPICS_PATHS[@]}"; do
        if [ -d "$path" ]; then
            export EPICS_BASE="$path"
            print_info "自动检测到 EPICS_BASE = $EPICS_BASE"
            break
        fi
    done
    
    if [ -z "$EPICS_BASE" ]; then
        print_warning "未找到 EPICS_BASE，请手动设置:"
        print_warning "export EPICS_BASE=/path/to/epics/base"
    fi
else
    print_info "使用现有 EPICS_BASE = $EPICS_BASE"
fi

# 设置 EPICS_HOST_ARCH
if [ -z "$EPICS_HOST_ARCH" ]; then
    if [ -n "$EPICS_BASE" ] && [ -f "$EPICS_BASE/startup/EpicsHostArch" ]; then
        export EPICS_HOST_ARCH=$($EPICS_BASE/startup/EpicsHostArch)
        print_info "检测到 EPICS_HOST_ARCH = $EPICS_HOST_ARCH"
    else
        # 默认架构检测
        case "$(uname -m)" in
            x86_64)
                export EPICS_HOST_ARCH="linux-x86_64"
                ;;
            i*86)
                export EPICS_HOST_ARCH="linux-x86"
                ;;
            aarch64)
                export EPICS_HOST_ARCH="linux-aarch64"
                ;;
            *)
                print_warning "无法自动检测架构，请手动设置 EPICS_HOST_ARCH"
                ;;
        esac
        print_info "设置 EPICS_HOST_ARCH = $EPICS_HOST_ARCH"
    fi
fi

# 添加 EPICS 工具到 PATH
if [ -n "$EPICS_BASE" ] && [ -d "$EPICS_BASE/bin/$EPICS_HOST_ARCH" ]; then
    export PATH="$EPICS_BASE/bin/$EPICS_HOST_ARCH:$PATH"
    print_info "添加 EPICS 工具到 PATH"
fi

# 添加 ipmiComm 工具到 PATH
if [ -d "$IPMICOMM_TOP/bin/$EPICS_HOST_ARCH" ]; then
    export PATH="$IPMICOMM_TOP/bin/$EPICS_HOST_ARCH:$PATH"
    print_info "添加 ipmiComm 工具到 PATH"
fi

# 设置库路径
if [ -d "$IPMICOMM_TOP/lib/$EPICS_HOST_ARCH" ]; then
    export LD_LIBRARY_PATH="$IPMICOMM_TOP/lib/$EPICS_HOST_ARCH:$LD_LIBRARY_PATH"
    print_info "添加 ipmiComm 库到 LD_LIBRARY_PATH"
fi

# 创建有用的别名
alias ipmicomm-build='cd $IPMICOMM_TOP && ./build.sh'
alias ipmicomm-clean='cd $IPMICOMM_TOP && ./build.sh clean'
alias ipmicomm-test='cd $IPMICOMM_TOP/iocBoot/sioc-ipmicomm-test'

print_info "环境设置完成!"
print_info ""
print_info "可用命令:"
print_info "  ipmicomm-build  - 编译项目"
print_info "  ipmicomm-clean  - 清理编译文件"
print_info "  ipmicomm-test   - 进入测试目录"
print_info ""
print_info "编译项目: ./build.sh"
print_info "运行测试: cd iocBoot/sioc-ipmicomm-test && ../../bin/$EPICS_HOST_ARCH/ipmiCommIoc st.cmd"
