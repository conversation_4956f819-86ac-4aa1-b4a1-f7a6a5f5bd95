4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 152
y 126
w 1008
h 700
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "Server - $(crat)"
showGrid
snapToGrid
gridSize 4
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 0
w 1008
h 40
lineColor index 54
fill
fillColor index 54
lineWidth 0
endObjectProperties

# (Exit Button)
object activeExitButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 912
y 8
w 48
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
label "Exit"
font "helvetica-medium-r-12.0"
3d
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 16
w 152
h 28
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 53
useDisplayBg
value {
  "$(crat)"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 824
y 672
w 168
h 16
controlPv "SIOC:SYS0:AL00:TOD"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
fontAlign "right"
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 848
y 8
w 56
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "Home..."
numPvs 4
numDsps 1
displayFileName {
  0 "lcls_main"
}
setPosition {
  0 "parentWindow"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 16
y 672
w 120
h 16
controlPv "SIOC:SYS0:AL00:MODE"
precision 1
fgColor index 14
bgColor index 12
font "helvetica-medium-r-12.0"
endObjectProperties

# (Embedded Window)
object activePipClass
beginObjectProperties
major 4
minor 1
release 0
x 432
y 56
w 560
h 224
fgColor index 14
bgColor index 3
topShadowColor index 1
botShadowColor index 11
displaySource "menu"
filePv "LOC\\cratPV=1"
sizeOfs 5
numDsps 1
displayFileName {
  0 "module_utca_info"
}
symbols {
  0 "id=FRU,unit=0"
}
noScroll
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 312
w 232
h 352
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 16
y 304
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Temperature Sensors"
}
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 264
y 312
w 232
h 352
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 264
y 304
w 128
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Voltages"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 336
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 336
w 91
h 24
controlPv "$(crat):TEMP1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 336
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 336
w 104
h 24
controlPv "$(crat):TEMP1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 368
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 368
w 91
h 24
controlPv "$(crat):TEMP2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 368
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 368
w 104
h 24
controlPv "$(crat):TEMP2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 400
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 400
w 91
h 24
controlPv "$(crat):TEMP3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 400
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 400
w 104
h 24
controlPv "$(crat):TEMP3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 432
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 432
w 91
h 24
controlPv "$(crat):TEMP4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 432
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 432
w 104
h 24
controlPv "$(crat):TEMP4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 464
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 464
w 91
h 24
controlPv "$(crat):TEMP5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 464
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 464
w 104
h 24
controlPv "$(crat):TEMP5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 336
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 336
w 88
h 24
controlPv "$(crat):V1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 336
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 336
w 104
h 24
controlPv "$(crat):V1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 368
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 368
w 88
h 24
controlPv "$(crat):V2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 368
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 368
w 104
h 24
controlPv "$(crat):V2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 400
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 400
w 88
h 24
controlPv "$(crat):V3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 400
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 400
w 104
h 24
controlPv "$(crat):V3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 432
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 432
w 88
h 24
controlPv "$(crat):V4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 432
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 432
w 104
h 24
controlPv "$(crat):V4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 464
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 464
w 88
h 24
controlPv "$(crat):V5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 464
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 464
w 104
h 24
controlPv "$(crat):V5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 496
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 496
w 88
h 24
controlPv "$(crat):V6"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 496
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 496
w 104
h 24
controlPv "$(crat):V6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 528
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 528
w 88
h 24
controlPv "$(crat):V7"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 528
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 528
w 104
h 24
controlPv "$(crat):V7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 560
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 560
w 88
h 24
controlPv "$(crat):V8"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 560
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 560
w 104
h 24
controlPv "$(crat):V8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 592
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 592
w 88
h 24
controlPv "$(crat):V9"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 592
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 592
w 104
h 24
controlPv "$(crat):V9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V9P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 272
y 624
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 392
y 624
w 88
h 24
controlPv "$(crat):V10"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 392
y 624
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):V10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 272
y 624
w 104
h 24
controlPv "$(crat):V10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):V10P"
visMin "1"
visMax "2"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 512
y 312
w 232
h 352
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 336
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 336
w 91
h 24
controlPv "$(crat):I1"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 336
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 336
w 104
h 24
controlPv "$(crat):I1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 368
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 368
w 91
h 24
controlPv "$(crat):I2"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 368
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 368
w 104
h 24
controlPv "$(crat):I2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 400
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 400
w 91
h 24
controlPv "$(crat):I3"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 400
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 400
w 104
h 24
controlPv "$(crat):I3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 432
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 432
w 91
h 24
controlPv "$(crat):I4"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 432
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 432
w 104
h 24
controlPv "$(crat):I4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 464
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 464
w 91
h 24
controlPv "$(crat):I5"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 464
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 464
w 104
h 24
controlPv "$(crat):I5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 496
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 496
w 91
h 24
controlPv "$(crat):I6"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 496
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 496
w 104
h 24
controlPv "$(crat):I6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 528
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 528
w 91
h 24
controlPv "$(crat):I7"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 528
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 528
w 104
h 24
controlPv "$(crat):I7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 560
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 560
w 91
h 24
controlPv "$(crat):I8"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 560
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 560
w 104
h 24
controlPv "$(crat):I8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 592
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 592
w 91
h 24
controlPv "$(crat):I9"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 592
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 592
w 104
h 24
controlPv "$(crat):I9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I9P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 520
y 624
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 640
y 624
w 91
h 24
controlPv "$(crat):I10"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 640
y 624
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):I10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 520
y 624
w 104
h 24
controlPv "$(crat):I10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):I10P"
visMin "1"
visMax "2"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 512
y 304
w 136
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Current Monitors"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 624
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 624
w 91
h 24
controlPv "$(crat):TEMP10"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 624
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 624
w 104
h 24
controlPv "$(crat):TEMP10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP10P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 496
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 496
w 91
h 24
controlPv "$(crat):TEMP6"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 496
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 496
w 104
h 24
controlPv "$(crat):TEMP6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 528
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 528
w 91
h 24
controlPv "$(crat):TEMP7"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 528
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 528
w 104
h 24
controlPv "$(crat):TEMP7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 560
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 560
w 91
h 24
controlPv "$(crat):TEMP8"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 560
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 560
w 104
h 24
controlPv "$(crat):TEMP8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 24
y 592
w 211
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 144
y 592
w 91
h 24
controlPv "$(crat):TEMP9"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 144
y 592
w 91
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):TEMP9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 24
y 592
w 104
h 24
controlPv "$(crat):TEMP9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):TEMP9P"
visMin "1"
visMax "2"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 760
y 320
w 232
h 344
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 760
y 304
w 88
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Cooling Fans"
}
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 336
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 336
w 88
h 24
controlPv "$(crat):FANSPEED1"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 336
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED1"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 336
w 104
h 24
controlPv "$(crat):FANSPEED1.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED1P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 368
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 368
w 88
h 24
controlPv "$(crat):FANSPEED2"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 368
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED2"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 368
w 104
h 24
controlPv "$(crat):FANSPEED2.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED2P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 400
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 400
w 88
h 24
controlPv "$(crat):FANSPEED3"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 400
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED3"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 400
w 104
h 24
controlPv "$(crat):FANSPEED3.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED3P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 432
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 432
w 88
h 24
controlPv "$(crat):FANSPEED4"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 432
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED4"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 432
w 104
h 24
controlPv "$(crat):FANSPEED4.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED4P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 464
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 464
w 88
h 24
controlPv "$(crat):FANSPEED5"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 464
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED5"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 464
w 104
h 24
controlPv "$(crat):FANSPEED5.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED5P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 496
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 496
w 88
h 24
controlPv "$(crat):FANSPEED6"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 496
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED6"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 496
w 104
h 24
controlPv "$(crat):FANSPEED6.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED6P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 528
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 528
w 88
h 24
controlPv "$(crat):FANSPEED7"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 528
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED7"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 528
w 104
h 24
controlPv "$(crat):FANSPEED7.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED7P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 560
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 560
w 88
h 24
controlPv "$(crat):FANSPEED8"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 560
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED8"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 560
w 104
h 24
controlPv "$(crat):FANSPEED8.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED8P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 592
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 592
w 88
h 24
controlPv "$(crat):FANSPEED9"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 592
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED9"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 592
w 104
h 24
controlPv "$(crat):FANSPEED9.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED9P"
visMin "1"
visMax "2"
endObjectProperties

# (Group)
object activeGroupClass
beginObjectProperties
major 4
minor 0
release 0
x 768
y 624
w 208
h 24

beginGroup

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 888
y 624
w 88
h 24
controlPv "$(crat):FANSPEED10"
displayMode "decimal"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 888
y 624
w 88
h 24
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
invisible
numPvs 4
numDsps 1
displayFileName {
  0 "crat_limits"
}
setPosition {
  0 "parentWindow"
}
symbols {
  0 "dev=$(crat):FANSPEED10"
}
replaceSymbols {
  0 1
}
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 768
y 624
w 104
h 24
controlPv "$(crat):FANSPEED10.DESC"
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

endGroup

visPv "$(crat):FANSPEED10P"
visMin "1"
visMax "2"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 156
y 24
w 160
h 16
controlPv "$(crat):LOC"
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 8
y 64
w 416
h 216
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 112
y 88
w 88
h 24
controlPv "$(crat):ONLNSTAT"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 88
w 96
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Network"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 128
w 96
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Communication"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 56
w 176
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "System Status and Control"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 112
y 128
w 88
h 24
controlPv "$(crat):INIT"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 308
y 128
w 108
h 32
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 0
useDisplayBg
visPv "$(crat):INIT"
visMin "1"
visMax "2"
value {
  "Re-initializing takes"
  "tens of seconds"
}
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 24
y 216
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):POWEROFF"
pressValue "0"
onLabel "Power Off"
offLabel "Power Off"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 120
y 216
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):POWERON"
pressValue "1"
onLabel "Power On"
offLabel "Power On"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 216
y 216
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):HARDRESET"
pressValue "3"
onLabel "Hard Reset"
offLabel "Hard Reset"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 112
y 168
w 88
h 24
controlPv "$(crat):POWERSTATE"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Related Display)
object relatedDisplayClass
beginObjectProperties
major 4
minor 4
release 0
x 288
y 256
w 48
h 20
fgColor index 14
bgColor index 4
topShadowColor index 1
botShadowColor index 11
font "helvetica-medium-r-12.0"
buttonLabel "Info..."
numPvs 4
numDsps 1
displayFileName {
  0 "crat_ipmi_chassis_details"
}
setPosition {
  0 "parentWindow"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 8
y 168
w 96
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Chassis Power"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 208
y 168
w 96
h 24
controlPv "$(crat):POWERMSG"
fgColor index 14
bgColor index 12
font "helvetica-medium-r-10.0"
fontAlign "center"
endObjectProperties

# (Menu Button)
object activeMenuButtonClass
beginObjectProperties
major 4
minor 0
release 0
x 208
y 128
w 92
h 28
fgColor index 14
fgAlarm
bgColor index 3
inconsistentColor index 5
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):CONNECT"
font "helvetica-medium-r-10.0"
colorPv "$(crat):CONNECT"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 8
y 4
w 160
h 16
controlPv "$(crat):TYPE_RAW"
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

