4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 808
y 250
w 300
h 400
font "helvetica-medium-r-10.0"
ctlFont "helvetica-medium-r-10.0"
btnFont "helvetica-medium-r-10.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 25
ctlFgColor2 index 18
ctlBgColor1 index 3
ctlBgColor2 index 5
topShadowColor index 1
botShadowColor index 11
title "MicroTCA MCH - $(crat)"
showGrid
snapToGrid
gridSize 8
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 8
w 304
h 400
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 0
w 128
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Module Power"
}
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 16
y 32
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):$(id)$(unit):POWERCTL"
pressValue "0"
onLabel "Deactivate"
offLabel "Deactivate"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

# (Message Button)
object activeMessageButtonClass
beginObjectProperties
major 4
minor 1
release 0
x 120
y 32
w 80
h 24
fgColor index 14
onColor index 4
offColor index 4
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):$(id)$(unit):POWERCTL"
pressValue "1"
onLabel "Activate"
offLabel "Activate"
3d
useEnumNumeric
font "helvetica-medium-r-12.0"
endObjectProperties

# (Byte)
object ByteClass
beginObjectProperties
major 4
minor 0
release 0
x 16
y 72
w 32
h 192
controlPv "$(crat):$(id)$(unit):MSTATE.RVAL"
lineColor index 14
onColor index 25
offColor index 3
endian "little"
numBits 8
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 72
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.ONST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 96
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.TWST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 120
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.THST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 144
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.FRST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 168
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.FVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 192
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.SXST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 240
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.EIST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Text Monitor)
object activeXTextDspClass:noedit
beginObjectProperties
major 4
minor 7
release 0
x 56
y 216
w 152
h 24
controlPv "$(crat):$(id)$(unit):MSTATE.SVST"
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
limitsFromDb
nullColor index 18
useHexPrefix
newPos
objType "monitors"
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 112
y 96
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power off)"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 104
y 168
w 104
h 24
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 3
useDisplayBg
value {
  "(power on)"
}
endObjectProperties

