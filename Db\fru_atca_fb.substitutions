#==============================================================================
# Abs:  ATCA Front Board (FB) FRU PV subset
#
# Name:  fru_atca_fb.substitutions
#
# Macros in:
#	dev      Shelf name, for example CRAT:LI28:RF01
#	prefix   String in PV name that identifies the FRU sensor is associated with (if there is one)
#		 PV name syntax: $(dev):$(prefix)$(attr)$(sensinst)
#		 For atca systems, prefix is "<frutype><instance>_", for example FB1_ for the first FB
#		 or CU2_ for the second cooling unit
#		 For supermicro, the sensors are not associated with a FRU, so prefix is an empty string
#	aliasprefix Temporary string to keep PV names backward-compatible via alias
#	id       String identifier, for example 'CU' for cooling unit
#       unit     Instance of this type of module, for example 2 for the second MCH in a carrier
#       attr	 Used in PV attribute, for example FAN1SPEED or TEMP2
#       sensinst Instance of this type of sensor on this module
#       type     Sensor type code, defined in IPMI spec (and src/ipmiDef.h)
#       fruid    FRU ID according to MicroTCA spec
#	dev      Shelf name, for example CRAT:LI28:RF01
#	cod      String identifier, for example 'CU' for cooling unit
#       inst     Instance of this type of module, for example 2 for the second MCH in a carrier
#       attr	 Used in PV attribute, for example FAN1SPEED or TEMP2
#       sensinst Instance of this type of sensor on this module
#       type     Sensor type code, defined in IPMI spec (and src/ipmiDef.h)
#       fruid    FRU ID according to MicroTCA spec
#
#==============================================================================
#

file fru_common.db
{
   pattern { dev	, id		, unit		, fruid		}
           { $(dev)	, $(id)		, $(unit)  	, $(fruid)	}
}

file sensor_ai.db
{
   pattern { dev	, prefix	, aliasprefix	,  attr		, sensinst	, type	, fruid 	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 1		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 2		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 3		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 4		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 5		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 6		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 7		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 8		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 9		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 10		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 11		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 12		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 13		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 14		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 15		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 16		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 17		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 18		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 19		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  TEMP		, 20		, 1	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 1		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 2		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 3		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 4		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 5		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 6		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 7		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 8		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 9		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 10		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 11		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 12		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 13		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 14		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 15		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 16		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 17		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 18		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 19		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  V		, 20		, 2	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  I		, 1		, 3	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  I		, 2		, 3	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  I		, 3		, 3	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  I		, 4		, 3	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  I		, 5		, 3	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  I		, 6		, 3	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  I		, 7		, 3	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  I		, 8		, 3	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  I		, 9		, 3	, $(fruid)	}
           { $(dev)	, $(id)$(unit):	, $(id)$(unit)_	,  I		, 10		, 3	, $(fruid)	}
}	   		  			
