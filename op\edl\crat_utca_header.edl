4 0 1
beginScreenProperties
major 4
minor 0
release 1
x 702
y 328
w 972
h 752
font "helvetica-medium-r-12.0"
ctlFont "helvetica-medium-r-12.0"
btnFont "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 7
textColor index 14
ctlFgColor1 index 14
ctlFgColor2 index 14
ctlBgColor1 index 4
ctlBgColor2 index 4
topShadowColor index 1
botShadowColor index 11
title "MicroTCA crate - $(crat)"
showGrid
snapToGrid
gridSize 4
endScreenProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 460
y 8
w 392
h 164
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Rectangle)
object activeRectangleClass
beginObjectProperties
major 4
minor 0
release 0
x 0
y 8
w 448
h 164
lineColor index 3
fill
fillColor index 3
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 484
y 12
w 380
h 152
font "helvetica-medium-r-12.0"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "AMC    - Advanced Mezzanine Card    - card in front of crate"
  "RTM     - Rear Transition Module          - card in rear of crate"
  "MCH    - MicroTCA Carrier Hub           - crate controller"
  "CU       - Cooling Unit                          - crate fan unit"
  "PM       - Power Module                      - crate power supply"
  "Carrier  - crate"
  "Shelf    - series of connected crates"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 100
y 32
w 88
h 24
controlPv "$(crat):ONLNSTAT"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 32
w 96
h 24
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Network"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 76
w 96
h 20
font "helvetica-medium-r-12.0"
fontAlign "right"
fgColor index 14
bgColor index 0
useDisplayBg
value {
  "Communication"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 0
y 0
w 176
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "System Status and Control"
}
endObjectProperties

# (Text Update)
object TextupdateClass
beginObjectProperties
major 10
minor 0
release 0
x 100
y 76
w 88
h 24
controlPv "$(crat):INIT"
fgColor index 16
fgAlarm
bgColor index 12
fill
font "helvetica-medium-r-12.0"
fontAlign "center"
lineWidth 2
lineAlarm
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 460
y 0
w 100
h 16
font "helvetica-medium-r-12.0"
fontAlign "center"
fgColor index 14
bgColor index 3
value {
  "Terms"
}
endObjectProperties

# (Static Text)
object activeXTextClass
beginObjectProperties
major 4
minor 1
release 1
x 296
y 76
w 108
h 32
font "helvetica-medium-i-12.0"
fgColor index 14
bgColor index 0
useDisplayBg
visPv "$(crat):INIT"
visMin "1"
visMax "2"
value {
  "Re-initializing takes"
  "tens of seconds"
}
endObjectProperties

# (Menu Button)
object activeMenuButtonClass
beginObjectProperties
major 4
minor 0
release 0
x 196
y 76
w 92
h 28
fgColor index 14
bgColor index 3
inconsistentColor index 5
topShadowColor index 1
botShadowColor index 11
controlPv "$(crat):CONNECT"
font "helvetica-medium-r-10.0"
endObjectProperties

